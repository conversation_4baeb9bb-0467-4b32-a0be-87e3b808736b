// Import for StreamSubscription
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:cat_tv/l10n/app_localizations.dart';
import 'package:media_kit/media_kit.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:cat_tv/pages/home_page.dart';
import 'package:cat_tv/services/favorites_service.dart';
import 'package:provider/provider.dart';
import 'package:cat_tv/providers/locale_provider.dart';
import 'package:cat_tv/providers/player_provider.dart'; // Import PlayerProvider
import 'package:cat_tv/services/mixpanel_service.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:cat_tv/services/iptv_data_service.dart';
import 'package:cat_tv/services/stream_url_builder_service.dart';
import 'package:cat_tv/utils/image_cache_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Configure image cache for better performance
  ImageCacheConfig.configure();

  if (kDebugMode) {
    final appSupportDir = await getApplicationSupportDirectory();
    print("Application Support Directory: ${appSupportDir.path}");
  }

  await MixpanelService().init();

  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  MediaKit.ensureInitialized();

  await FavoritesService.init();

  final iptvDataService = IptvDataService();
  final streamUrlBuilderService = StreamUrlBuilderService(iptvDataService);

  await streamUrlBuilderService.buildProvider2StreamUrl('dummy_source_id');

  final Map<String, dynamic>? iptvData = await iptvDataService.loadIptvData();
  if (iptvData != null) {
    debugPrint("Loaded IPTV Data from SharedPreferences: $iptvData");
  } else {
    debugPrint("No IPTV data found in SharedPreferences.");
  }

  final localeProvider = LocaleProvider(const Locale('en'));
  await localeProvider.loadLocale();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider<LocaleProvider>.value(value: localeProvider),
        ChangeNotifierProvider<PlayerProvider>(create: (_) => PlayerProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // No need to load locale here, it's loaded in main()
  }

  @override
  void dispose() {
    debugPrint("app closed");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Cat TV',
      locale:
          context.watch<LocaleProvider>().locale, // Use locale from provider
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales:
          AppLocalizations
              .supportedLocales, // Use supportedLocales from AppLocalizations
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      routes: {'/': (context) => const HomePage()},
      initialRoute: '/',
      debugShowCheckedModeBanner: false,
    );
  }
}
