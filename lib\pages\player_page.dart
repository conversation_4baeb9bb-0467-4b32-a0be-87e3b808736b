import 'dart:async';
import 'package:flutter/material.dart';
import 'dart:ui'; // Required for ImageFilter
import 'package:flutter/services.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:flutter_hls_parser/flutter_hls_parser.dart'
    as hls_parser; // Keep if needed for quality switching logic
import 'package:http/http.dart' as http;
import 'package:cat_tv/services/iptv_data_service.dart'; // Import IptvDataService
import 'package:cat_tv/services/stream_url_builder_service.dart'; // Import StreamUrlBuilderService
import 'package:cat_tv/widgets/player_app_bar_title.dart'; // Import the new widget
import 'package:cat_tv/widgets/channel_source_list.dart'; // Import the new ChannelSourceList widget
import 'package:provider/provider.dart'; // Import provider
import 'package:cat_tv/providers/player_provider.dart'; // Import PlayerProvider

import 'package:cat_tv/models/channel.dart';

class PlayerPage extends StatefulWidget {
  final Channel channel;
  final List<Map<String, dynamic>> channelSources; // Changed to a list of maps
  const PlayerPage({
    super.key,
    required this.channel,
    required this.channelSources, // Updated to list of maps
  });

  @override
  State<PlayerPage> createState() => _PlayerPageState();
}

class _PlayerPageState extends State<PlayerPage> with TickerProviderStateMixin {
  static const platform = MethodChannel('com.cat_tv/window_manager');

  late final Player player;
  late final VideoController controller;

  // Player state variables
  bool _hasSource = false;
  bool _isBuffering = false;
  String? _errorMessage;
  final List<hls_parser.Variant> _availableQualities = [];
  bool _isLiveStream = false; // New: for live stream indicator
  int _currentSourceIndex = 0; // New: to track the active source

  late final IptvDataService _iptvDataService;
  late final StreamUrlBuilderService _streamUrlBuilderService;

  // UI state variables
  late bool _isFullScreen;

  // Additional features state
  String? _currentQuality;
  String? _currentSubtitle;
  final double _playbackSpeed = 1.0;

  // Stream subscriptions
  late StreamSubscription<bool> _bufferingSubscription;
  late StreamSubscription<String> _errorSubscription;
  late StreamSubscription<Playlist> _playlistSubscription;

  @override
  void initState() {
    super.initState();
    _isFullScreen = false; // Initial state for player page

    // Get player instance from provider
    player = context.read<PlayerProvider>().player;
    controller = VideoController(player);

    // Request focus for keyboard events
    FocusManager.instance.primaryFocus?.requestFocus();

    // Listen to player streams
    _bufferingSubscription = player.stream.buffering.listen((buffering) {
      setState(() {});
    });
    _errorSubscription = player.stream.error.listen((error) {
      setState(() {
        // Check if the error message contains the specific phrase to hide it
        // Check if the error message contains the specific phrase to hide it
        if (error.contains('Error decoding audio.')) {
          _errorMessage = null; // Or an empty string ''
        } else {
          _errorMessage = error;
        }
      });
    });
    _playlistSubscription = player.stream.playlist.listen((playlist) {
      setState(() {
        _hasSource = playlist.medias.isNotEmpty;
        if (_hasSource) {
          // Determine if it's a live stream (simple check, can be improved)
          _isLiveStream = playlist.medias.first.uri.contains(
            'm3u8',
          ); // Example heuristic
        } else {
          _isLiveStream = false;
        }
      });
    });

    _iptvDataService = IptvDataService();
    _streamUrlBuilderService = StreamUrlBuilderService(_iptvDataService);

    // Load the channel URL passed from the previous page
    _loadSource(
      widget.channelSources[_currentSourceIndex],
    ); // Load the first source
  }

  @override
  void didUpdateWidget(PlayerPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If the channel sources change, load the first one
    if (widget.channelSources != oldWidget.channelSources) {
      _currentSourceIndex = 0; // Reset to first source if channel changes
      _loadSource(widget.channelSources[_currentSourceIndex]);
    }
  }

  @override
  void dispose() {
    _bufferingSubscription.cancel();
    _errorSubscription.cancel();
    _playlistSubscription.cancel();
    // Player is stopped when navigating back, not in dispose to avoid deactivated context issues.
    super.dispose();
  }

  Future<void> _loadSource(Map<String, dynamic> source) async {
    final String sourceUrl = source['source_url'] as String;
    final int providerId = source['provider_id'] as int;

    String? finalUrl;

    if (providerId == 2) {
      finalUrl = await _streamUrlBuilderService.buildProvider2StreamUrl(
        sourceUrl,
      );
    } else {
      finalUrl = sourceUrl;
    }

    if (finalUrl == null || finalUrl.isEmpty) {
      if (!mounted) return;
      setState(() {
        _hasSource = false;
        _isBuffering = false;
        _errorMessage = 'Failed to build stream URL for this source.';
        _availableQualities.clear();
        _currentQuality = null;
      });
      player.stop();
      return;
    }

    if (!mounted) return;
    setState(() {
      _hasSource = true;
      _errorMessage = null; // Clear any previous error
      _availableQualities.clear(); // Clear previous qualities
      _currentQuality = null; // Reset current quality
    });

    // Log the final stream URL
    debugPrint('Playing source URL: $finalUrl');

    // Log window size when a channel is loaded/played
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final Size currentSize = MediaQuery.of(context).size;
      debugPrint('Window size when playing channel: $currentSize');
    });

    if (finalUrl.endsWith('.m3u8')) {
      try {
        // First, fetch the m3u8 content
        final response = await http.get(Uri.parse(finalUrl));
        if (response.statusCode == 200) {
          final playlist = await hls_parser.HlsPlaylistParser.create()
              .parseString(Uri.parse(finalUrl), response.body);

          if (playlist is hls_parser.HlsMasterPlaylist) {
            if (!mounted) return;
            setState(() {
              _availableQualities.addAll(playlist.variants);
              // Sort qualities by resolution or bitrate for better display
              _availableQualities.sort((a, b) {
                final aRes = (a.format.width ?? 0) * (a.format.height ?? 0);
                final bRes = (b.format.width ?? 0) * (b.format.height ?? 0);
                if (aRes != bRes) return aRes.compareTo(bRes);
                return (a.format.bitrate ?? 0).compareTo(b.format.bitrate ?? 0);
              });
            });
          }
        } else {
          throw Exception('Failed to fetch m3u8: ${response.statusCode}');
        }
      } catch (e) {
        if (!mounted) return;
        setState(() {
          _errorMessage = 'Failed to parse HLS stream: $e';
        });
        player.stop();
        return;
      }
    }

    player.open(Media(finalUrl));
  }

  void _toggleFullScreen() async {
    setState(() {
      _isFullScreen = !_isFullScreen;
    });

    if (_isFullScreen) {
      debugPrint('Entering fullscreen mode');
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    } else {
      debugPrint('Exiting fullscreen mode');
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }

    // Call native method to toggle window fullscreen state
    try {
      await platform.invokeMethod('toggleFullScreen');
    } on PlatformException catch (e) {
      debugPrint("Failed to toggle window fullscreen: '${e.message}'.");
    }

    // Get the size after the state change and system UI mode update
    // This will reflect the new orientation and UI mode.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final Size currentSize = MediaQuery.of(context).size;
      debugPrint(
        'Window size after ${_isFullScreen ? "entering" : "exiting"} fullscreen: $currentSize',
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    // Determine the appropriate widget based on fullscreen state
    Widget playerWidget = Video(
      controller: controller,
      controls: AdaptiveVideoControls,
    );

    return WillPopScope(
      onWillPop: () async {
        // Stop the player when the system back button is pressed
        context.read<PlayerProvider>().stopPlayer();
        return true; // Allow the pop to proceed
      },
      child: KeyboardListener(
        focusNode: FocusNode(),
        onKeyEvent: (KeyEvent event) {
          if (event is KeyDownEvent) {
            if (event.logicalKey == LogicalKeyboardKey.escape) {
              if (_isFullScreen) {
                _toggleFullScreen();
              }
            } else if (event.logicalKey == LogicalKeyboardKey.enter) {
              _toggleFullScreen();
            }
          }
        },
        child: Focus(
          autofocus: true,
          child:
              _isFullScreen
                  ? Scaffold(
                    backgroundColor: Colors.black,
                    appBar: PreferredSize(
                      preferredSize: Size.zero,
                      child: AppBar(
                        toolbarHeight: 0,
                        backgroundColor: Colors.transparent,
                        elevation: 0,
                      ),
                    ),
                    body: playerWidget,
                    extendBodyBehindAppBar: true,
                    extendBody: true,
                    resizeToAvoidBottomInset: false,
                  )
                  : Scaffold(
                    extendBodyBehindAppBar: true,
                    backgroundColor: Colors.black,
                    appBar: AppBar(
                      title: Text(
                        widget.channel.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 20,
                          color: Colors.white,
                        ),
                      ),
                      backgroundColor: Colors.transparent,
                      elevation: 0,
                      centerTitle: false,
                      flexibleSpace: ClipRRect(
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                          child: Container(
                            decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Color.fromARGB(255, 50, 0, 50), // Dark purple
                                  Color.fromARGB(255, 20, 20, 20), // Dark grey
                                ],
                              ),
                              border: Border(
                                bottom: BorderSide(
                                  color: Colors.white10,
                                  width: 0.5,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      leading: IconButton(
                        icon: const Icon(Icons.arrow_back, color: Colors.white),
                        onPressed: () {
                          context
                              .read<PlayerProvider>()
                              .stopPlayer(); // Stop the player before navigating back
                          Navigator.of(context).pop();
                        },
                        tooltip: 'Back to Channels',
                      ),
                      actions: [
                        Builder(
                          builder: (BuildContext context) {
                            return IconButton(
                              icon: const Icon(Icons.menu, color: Colors.white),
                              onPressed: () {
                                Scaffold.of(
                                  context,
                                ).openEndDrawer(); // Open from right
                              },
                              tooltip: 'Open Sources',
                            );
                          },
                        ),
                      ],
                    ),
                    endDrawer: Drawer(
                      // Use endDrawer to open from the right
                      backgroundColor: const Color.fromARGB(255, 20, 20, 20),
                      child: SafeArea(
                        child: ChannelSourceList(
                          channelSources: widget.channelSources,
                          currentSourceIndex: _currentSourceIndex,
                          onLoadSource: (newIndex) {
                            setState(() {
                              _currentSourceIndex = newIndex;
                            });
                            _loadSource(widget.channelSources[newIndex]);
                            Navigator.of(
                              context,
                            ).pop(); // Close drawer after selection
                          },
                          channel: widget.channel,
                        ),
                      ),
                    ),
                    body: Container(
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Color.fromARGB(255, 20, 20, 20),
                            Color.fromARGB(255, 50, 0, 50),
                          ],
                        ),
                      ),
                      child: SafeArea(
                        child: Center(
                          child: playerWidget,
                        ), // Player takes full width
                      ),
                    ),
                  ),
        ),
      ),
    );
  }
}
