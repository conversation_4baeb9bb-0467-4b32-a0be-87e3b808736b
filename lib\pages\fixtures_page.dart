import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:html/parser.dart' show parse;
import 'package:url_launcher/url_launcher.dart'; // For launching URLs in browser
import 'package:shared_preferences/shared_preferences.dart'; // For shared preferences
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'dart:io';
import 'dart:convert';
import 'dart:async'; // Import for Completer
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:cat_tv/services/webview_utils.dart'; // Import WebViewUtils
import 'package:cat_tv/pages/webview_page.dart'; // Keep for non-headless mode
import 'package:cat_tv/utils/cloudflare_handler.dart'; // Import CloudflareHandler

class FixturesPage extends StatefulWidget {
  const FixturesPage({super.key});

  @override
  State<FixturesPage> createState() => _FixturesPageState();
}

class _FixturesPageState extends State<FixturesPage> {
  String _statusMessage = '';
  bool _isLoading = false;
  bool _isOpeningLink = false; // New state for link opening
  bool _isRefreshing = false; // New state for refreshing
  List<dynamic> _scrapedData = []; // New state to hold scraped data
  late ScrollController _scrollController; // Declare ScrollController
  DateTime? _lastRefreshed; // New state for last refreshed time

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController(); // Initialize ScrollController
    _loadScrapedData(); // Load data when the page initializes
    _loadLastRefreshed(); // Load last refreshed time
  }

  Future<void> _loadLastRefreshed() async {
    final prefs = await SharedPreferences.getInstance();
    final lastRefreshedMillis = prefs.getInt('last_refreshed_fixtures');
    if (lastRefreshedMillis != null) {
      setState(() {
        _lastRefreshed = DateTime.fromMillisecondsSinceEpoch(
          lastRefreshedMillis,
        );
      });
    }
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) {
      return 'Never';
    }
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hr ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  @override
  void dispose() {
    _scrollController.dispose(); // Dispose ScrollController
    super.dispose();
  }

  Future<void> _loadScrapedData() async {
    setState(() {
      _statusMessage = 'Loading scraped data...';
    });
    try {
      final directory = await getApplicationDocumentsDirectory();
      final cachePath = p.join(directory.path, 'cache');
      final file = File(p.join(cachePath, 'fixtures.json'));

      if (await file.exists()) {
        final jsonString = await file.readAsString();
        setState(() {
          _scrapedData = jsonDecode(jsonString);
          _statusMessage = 'Fixtures data loaded successfully.';
        });
        debugPrint('Loaded fixtures content in console.');
      } else {
        setState(() {
          _statusMessage = 'No fixtures data found.';
        });
        debugPrint('No fixtures data file found.');
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading scraped data: $e';
      });
      debugPrint('Error loading scraped data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updateSite() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Fetching data...';
    });

    const url = 'https://sites.google.com/view/superabbit77link2';
    HeadlessInAppWebView? headlessWebView;
    // Use a Completer to wait for the JavaScript handler to return data
    final Completer<List<String>> completer = Completer<List<String>>();

    try {
      headlessWebView = HeadlessInAppWebView(
        initialUrlRequest: URLRequest(url: WebUri(url)),
        initialSettings: InAppWebViewSettings(
          javaScriptEnabled: true,
          isInspectable: true,
          javaScriptCanOpenWindowsAutomatically: true,
          mediaPlaybackRequiresUserGesture: false,
          allowsInlineMediaPlayback: true,
          iframeAllow: "camera; microphone",
          iframeAllowFullscreen: true,
          userAgent:
              'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Mobile Safari/537.36',
        ),
        onWebViewCreated: (controller) {
          debugPrint('Headless WebView for _updateSite created.');
          controller.addJavaScriptHandler(
            handlerName: 'googleSitesScrapeHandler',
            callback: (args) async {
              debugPrint('Google Sites scrape handler called with data: $args');
              if (args.isNotEmpty && args[0] is List) {
                final List<String> scraped = List<String>.from(args[0]);
                debugPrint('Scraped links from Google Sites: $scraped');
                if (!completer.isCompleted) {
                  completer.complete(scraped);
                }
              } else {
                if (!completer.isCompleted) {
                  completer.complete([]); // Complete with empty list if no data
                }
              }
            },
          );
        },
        onLoadStop: (controller, url) async {
          debugPrint('Headless WebView for _updateSite finished loading: $url');
          await WebViewUtils.injectAdBlockingScript(controller);
          await controller.evaluateJavascript(
            source: WebViewUtils.getGoogleSitesScrapingScript(),
          );
          // Wait for page to load then extract content
          await WebViewUtils.extractContent(controller);
          // If the script doesn't call the handler for some reason,
          // ensure the completer eventually completes to avoid a hang.
          // This is a fallback, ideally the JS should always call the handler.
          Future.delayed(const Duration(seconds: 5), () {
            if (!completer.isCompleted) {
              debugPrint(
                'Timeout: googleSitesScrapeHandler not called, completing with empty list.',
              );
              completer.complete([]);
            }
          });
        },
        onLoadError: (controller, url, code, message) {
          debugPrint(
            'Headless WebView for _updateSite Load Error: $url, $code, $message',
          );
          setState(() {
            _statusMessage = 'Failed to load page: $message (Code: $code)';
          });
          if (!completer.isCompleted) {
            completer.completeError(
              'Failed to load page: $message (Code: $code)',
            );
          }
        },
        onConsoleMessage: (controller, consoleMessage) {
          debugPrint(
            'Headless WebView for _updateSite Console: ${consoleMessage.message}',
          );
        },
      );

      debugPrint('Attempting to run headless WebView for _updateSite.');
      await headlessWebView.run();
      debugPrint('Headless WebView for _updateSite finished running.');

      // Wait for the links to be scraped by the JavaScript handler
      final List<String> extractedLinks = await completer.future;
      debugPrint(
        'Completer finished. Extracted links count: ${extractedLinks.length}',
      );

      if (extractedLinks.isNotEmpty) {
        final linksToSave = extractedLinks.take(2).toList();
        final prefs = await SharedPreferences.getInstance();
        await prefs.setStringList('fixture_links', linksToSave);

        setState(() {
          _statusMessage =
              'Found ${linksToSave.length} links and saved to preferences.';
        });
        for (var link in linksToSave) {
          debugPrint(
            'Saved Link: $link',
          ); // Changed to "Saved Link" for clarity
        }
      } else {
        setState(() {
          _statusMessage = 'No links found.';
        });
        debugPrint('No links were extracted from Google Sites.');
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: $e';
      });
      debugPrint('Error during site update: $e');
      if (!completer.isCompleted) {
        completer.completeError(e); // Ensure completer completes on error
      }
    } finally {
      headlessWebView?.dispose(); // Ensure the headless webview is disposed
      setState(() {
        _isLoading = false;
      });
      debugPrint('Headless WebView for _updateSite disposed.');
    }
  }

  Future<bool> _launchUrlInApp(String url, {bool headless = false}) async {
    final String finalUrl;
    if (headless) {
      const String baseUrl = 'https://koa.xte8titlen3edeast.shop/football.html';
      finalUrl = '$baseUrl?url=$url';
    } else {
      finalUrl = url; // Use the provided URL directly for non-headless mode
    }

    try {
      if (headless) {
        debugPrint('Launching headless WebView for: $finalUrl');
        final headlessWebView = HeadlessInAppWebView(
          initialUrlRequest: URLRequest(url: WebUri(finalUrl)),
          initialSettings: InAppWebViewSettings(
            javaScriptEnabled: true,
            isInspectable: true,
            javaScriptCanOpenWindowsAutomatically: true,
            mediaPlaybackRequiresUserGesture: false,
            allowsInlineMediaPlayback: true,
            iframeAllow: "camera; microphone",
            iframeAllowFullscreen: true,
            // Set user agent to mimic a mobile browser if needed for scraping
            userAgent:
                'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Mobile Safari/537.36',
          ),
          onWebViewCreated: (controller) {
            debugPrint('🚀 Headless WebView created for fixtures scraping');

            // Add scraping handler
            controller.addJavaScriptHandler(
              handlerName: 'scrapeHandler',
              callback: (args) async {
                debugPrint('📊 Scrape handler called with data: $args');
                if (args.isNotEmpty) {
                  await WebViewUtils.saveScrapedData(args[0]);
                  setState(() {
                    _scrapedData = args[0];
                  });
                }
              },
            );

            // Add Cloudflare detection handler
            controller.addJavaScriptHandler(
              handlerName: 'cloudflareDetected',
              callback: (args) async {
                if (args.isNotEmpty && args[0] is Map) {
                  final data = Map<String, dynamic>.from(args[0]);
                  debugPrint(
                    '🛡️ Cloudflare protection detected in headless mode',
                  );
                  debugPrint('🌐 URL: ${data['url']}');
                  debugPrint('🔍 Indicator: ${data['indicator']}');

                  // For headless mode, we'll try to wait and retry
                  await Future.delayed(const Duration(seconds: 5));
                  controller.reload();
                }
              },
            );

            // Add ad blocking handler
            controller.addJavaScriptHandler(
              handlerName: 'adBlocked',
              callback: (args) {
                if (args.isNotEmpty && args[0] is Map) {
                  final data = Map<String, dynamic>.from(args[0]);
                  debugPrint(
                    '🚫 Ad blocked in headless mode - Type: ${data['type']}',
                  );
                }
              },
            );
          },
          onLoadStop: (controller, url) async {
            debugPrint('Headless WebView finished loading: $url');
            // Inject ad blocking script
            await WebViewUtils.injectAdBlockingScript(controller);
            // Inject scraping script
            await controller.evaluateJavascript(
              source: WebViewUtils.getScrapingScript(),
            );
            // Wait for page to load then extract content
            await WebViewUtils.extractContent(controller);
            // The headless webview instance will be disposed after run() completes.
            debugPrint('Headless WebView finished its task.');
            return; // Explicitly return void
          },
          onLoadError: (controller, url, code, message) {
            debugPrint('Headless WebView Load Error: $url, $code, $message');
          },
          onConsoleMessage: (controller, consoleMessage) {
            debugPrint('Headless WebView Console: ${consoleMessage.message}');
          },
        );

        await headlessWebView.run();
        return true;
      } else {
        await showDialog(
          context: context,
          builder: (BuildContext context) {
            return Dialog(
              backgroundColor: Colors.black,
              insetPadding: const EdgeInsets.all(20),
              child: Stack(
                children: [
                  WebViewPage(
                    channelUrl: finalUrl,
                    initialWebViewSettings: InAppWebViewSettings(
                      javaScriptEnabled: true,
                      isInspectable: true,
                    ),
                  ),
                  Positioned(
                    top: 0,
                    right: 0,
                    child: IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      }
      return true;
    } catch (e) {
      debugPrint('Error navigating to WebViewPage or running headless: $e');
      return false;
    }
  }

  Future<void> _openFixtureLink() async {
    setState(() {
      _isOpeningLink = true;
      _statusMessage = 'Opening fixture link...';
    });

    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final List<String>? fixtureLinks = prefs.getStringList('fixture_links');

    if (fixtureLinks == null || fixtureLinks.isEmpty) {
      setState(() {
        _statusMessage = 'No fixture links found. Updating sites...';
      });
      await _updateSite(); // Update sites if no links are found initially
      final updatedLinks = prefs.getStringList('fixture_links');
      if (updatedLinks == null || updatedLinks.isEmpty) {
        setState(() {
          _statusMessage =
              'Failed to get links after update. Please try again.';
          _isOpeningLink = false;
        });
        return;
      }
      await _tryOpenLinks(updatedLinks); // Try opening links after update
    } else {
      await _tryOpenLinks(fixtureLinks);
    }

    // After opening links (and potentially updating site), save refresh time and reload data
    await _saveLastRefreshed();
    await _loadScrapedData(); // Refresh the displayed data
  }

  Future<void> _openFixtureLinkHeadless() async {
    setState(() {
      _isRefreshing = true; // Set refreshing to true
      _statusMessage = 'Refreshing...'; // Set initial status message
    });

    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final List<String>? fixtureLinks = prefs.getStringList('fixture_links');

      if (fixtureLinks == null || fixtureLinks.isEmpty) {
        setState(() {
          _statusMessage = 'No fixture links found. Updating sites...';
        });
        await _updateSite(); // Update sites if no links are found initially
        final updatedLinks = prefs.getStringList('fixture_links');
        if (updatedLinks == null || updatedLinks.isEmpty) {
          setState(() {
            _statusMessage =
                'Failed to get links after update. Please try again.';
            _isRefreshing = false; // Reset refreshing
          });
          return;
        }
        await _tryOpenLinks(
          updatedLinks,
          headless: true,
        ); // Try opening links after update
      } else {
        await _tryOpenLinks(fixtureLinks, headless: true);
      }

      // After opening links (and potentially updating site), save refresh time and reload data
      await _saveLastRefreshed();
      await _loadScrapedData(); // Refresh the displayed data
      setState(() {
        _statusMessage = 'Fixtures refreshed successfully.';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error during refresh: $e';
      });
      debugPrint('Error during headless fixture refresh: $e');
    } finally {
      setState(() {
        _isRefreshing = false; // Reset refreshing
      });
    }
  }

  Future<void> _tryOpenLinks(
    List<String> links, {
    bool headless = false,
  }) async {
    bool opened = false;
    setState(() {
      _isOpeningLink =
          true; // Set _isOpeningLink to true when starting to open links
    });
    if (links.isNotEmpty) {
      // Try link 1
      if (await _launchUrlInApp(links[0], headless: headless)) {
        opened = true;
        setState(() {
          _statusMessage = 'Opened link 1 successfully.';
        });
      } else if (links.length > 1) {
        // Try link 2 if link 1 failed and link 2 exists
        setState(() {
          _statusMessage = 'Link 1 failed. Trying link 2...';
        });
        if (await _launchUrlInApp(links[1], headless: headless)) {
          opened = true;
          setState(() {
            _statusMessage = 'Opened link 2 successfully.';
          });
        }
      }
    }

    if (!opened) {
      setState(() {
        _statusMessage =
            'Both links failed or no links available. Updating sites and retrying...';
      });
      await _updateSite(); // Perform update sites
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final updatedLinks = prefs.getStringList('fixture_links');
      if (updatedLinks != null && updatedLinks.isNotEmpty) {
        setState(() {
          _statusMessage = 'Sites updated. Retrying links...';
        });
        // Retry opening links after update
        if (await _launchUrlInApp(updatedLinks[0], headless: headless)) {
          opened = true;
          setState(() {
            _statusMessage = 'Opened updated link 1 successfully.';
          });
        } else if (updatedLinks.length > 1) {
          if (await _launchUrlInApp(updatedLinks[1], headless: headless)) {
            opened = true;
            setState(() {
              _statusMessage = 'Opened updated link 2 successfully.';
            });
          }
        }
      }
    }

    setState(() {
      _isOpeningLink =
          false; // Reset _isOpeningLink when done trying to open links
      if (!opened) {
        _statusMessage =
            'Failed to open any fixture links after multiple attempts.';
      }
    });
  }

  Future<void> _saveLastRefreshed() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    await prefs.setInt('last_refreshed_fixtures', now.millisecondsSinceEpoch);
    setState(() {
      _lastRefreshed = now;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header and Button
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color.fromARGB(255, 50, 0, 50), // Dark purple
                Color.fromARGB(255, 20, 20, 20), // Dark grey
              ],
            ),
            border: Border(
              bottom: BorderSide(color: Colors.white10, width: 0.5),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 12.0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Fixtures',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                  Row(
                    // Wrap buttons in a Row
                    children: [
                      ElevatedButton(
                        onPressed: _isLoading ? null : _updateSite,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.deepPurple,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.all(
                            12,
                          ), // Adjust padding for icon-only button
                          minimumSize: const Size(
                            48,
                            48,
                          ), // Ensure minimum size for touch target
                        ),
                        child:
                            _isLoading
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                                : const Icon(Icons.refresh),
                      ),
                      const SizedBox(width: 8), // Spacer between buttons
                      ElevatedButton(
                        onPressed:
                            _isOpeningLink || _isLoading
                                ? null
                                : _openFixtureLink,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blueAccent,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.all(12),
                          minimumSize: const Size(48, 48),
                        ),
                        child:
                            _isOpeningLink
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                                : const Icon(Icons.open_in_browser),
                      ),
                      const SizedBox(width: 8), // Spacer between buttons
                      ElevatedButton(
                        onPressed:
                            _isOpeningLink || _isLoading || _isRefreshing
                                ? null
                                : _openFixtureLinkHeadless,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Colors.green, // New color for distinction
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.all(12),
                          minimumSize: const Size(48, 48),
                        ),
                        child:
                            _isOpeningLink || _isRefreshing
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                                : const Icon(Icons.visibility_off),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        // Status/Last Refreshed display
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            _lastRefreshed != null &&
                    !_isLoading &&
                    !_isOpeningLink &&
                    !_isRefreshing
                ? 'Last Refreshed: ${_formatDateTime(_lastRefreshed)}'
                : _statusMessage.isNotEmpty
                ? _statusMessage
                : 'No fixtures to display. please click on refresh button".',
            style: const TextStyle(color: Colors.white70),
            textAlign: TextAlign.center,
          ),
        ),
        // Display scraped data
        Expanded(
          child:
              _scrapedData.isEmpty &&
                      !_isLoading &&
                      !_isOpeningLink &&
                      !_isRefreshing
                  ? Center(
                    child: Text(
                      _statusMessage.isNotEmpty && _lastRefreshed == null
                          ? _statusMessage
                          : 'No fixtures to display. Click "Update Site" or "Open Scraper".',
                      style: const TextStyle(
                        color: Colors.white54,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  )
                  : _isLoading || _isOpeningLink || _isRefreshing
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(color: Colors.white),
                        const SizedBox(height: 16),
                        Text(
                          _statusMessage,
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ],
                    ),
                  )
                  : Scrollbar(
                    controller:
                        _scrollController, // Assign controller to Scrollbar
                    thumbVisibility: true, // Make the scrollbar always visible
                    child: ListView.builder(
                      controller:
                          _scrollController, // Assign controller to ListView.builder
                      itemCount: _scrapedData.length,
                      itemBuilder: (context, index) {
                        final competitionData = _scrapedData[index];
                        final competitionName = competitionData['competition'];
                        final competitionLogo = competitionData['logo'];
                        final games = competitionData['games'] as List<dynamic>;

                        return Card(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 12.0,
                            vertical: 6.0,
                          ),
                          color: const Color.fromARGB(255, 30, 30, 30),
                          elevation: 4.0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          child: ExpansionTile(
                            backgroundColor: const Color.fromARGB(
                              255,
                              30,
                              30,
                              30,
                            ),
                            collapsedBackgroundColor: const Color.fromARGB(
                              255,
                              30,
                              30,
                              30,
                            ),
                            leading:
                                competitionLogo != null &&
                                        competitionLogo != 'N/A'
                                    ? Image.network(
                                      competitionLogo,
                                      width: 32,
                                      height: 32,
                                      errorBuilder:
                                          (context, error, stackTrace) =>
                                              const Icon(
                                                Icons.flag,
                                                color: Colors.white54,
                                                size: 32,
                                              ),
                                    )
                                    : const Icon(
                                      Icons.flag,
                                      color: Colors.white54,
                                      size: 32,
                                    ),
                            title: Text(
                              competitionName,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            iconColor: Colors.white,
                            collapsedIconColor: Colors.white70,
                            children:
                                games.map<Widget>((game) {
                                  final gameType = game['type'] ?? 'match';
                                  final homeTeam = game['home_team'] ?? {};
                                  final awayTeam = game['away_team'] ?? {};

                                  // Check if this is a real event (like Apex Legends) or a match without team data
                                  final isRealEvent =
                                      gameType == 'event' &&
                                      (homeTeam['name'] == 'N/A' ||
                                          homeTeam['name'] == null) &&
                                      (awayTeam['name'] == 'N/A' ||
                                          awayTeam['name'] == null) &&
                                      game['title'] != null &&
                                      game['title'] != 'N/A';

                                  return InkWell(
                                    onTap: () {
                                      final gameUrl = game['url'];
                                      if (gameUrl != null && gameUrl != 'N/A') {
                                        debugPrint('Opening game: $gameUrl');
                                        _launchUrlInApp(
                                          gameUrl,
                                          headless: false,
                                        );
                                      }
                                    },
                                    child: Container(
                                      margin: const EdgeInsets.symmetric(
                                        horizontal: 16.0,
                                        vertical: 8.0,
                                      ),
                                      padding: const EdgeInsets.all(12.0),
                                      decoration: BoxDecoration(
                                        color: const Color.fromARGB(
                                          255,
                                          35,
                                          0,
                                          35,
                                        ), // Darker purple
                                        borderRadius: BorderRadius.circular(
                                          8.0,
                                        ),
                                        border: Border.all(
                                          color: const Color.fromARGB(
                                            255,
                                            80,
                                            0,
                                            80,
                                          ), // Purple border
                                          width: 1.5,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withOpacity(
                                              0.4,
                                            ),
                                            spreadRadius: 1,
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child:
                                          isRealEvent
                                              ? _buildEventTile(game)
                                              : _buildMatchTile(game),
                                    ),
                                  );
                                }).toList(),
                          ),
                        );
                      },
                    ),
                  ), // Closing tag for Scrollbar
        ),
      ],
    );
  }

  Widget _buildEventTile(Map<String, dynamic> game) {
    final title = game['title'] ?? 'N/A';
    final time = game['time'] ?? 'N/A';
    final currentMinute = game['current_minute'] ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  time,
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                ),
                if (currentMinute.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6.0,
                      vertical: 2.0,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: Text(
                      currentMinute,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                else if (currentMinute.isEmpty && time != 'N/A')
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6.0,
                      vertical: 2.0,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: Text(
                      _getCountdownText(time),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMatchTile(Map<String, dynamic> game) {
    final time = game['time'] ?? 'N/A';
    final currentMinute = game['current_minute'] ?? '';
    final homeTeam = game['home_team'] ?? {};
    final awayTeam = game['away_team'] ?? {};

    // Check if game has started (has scores or current minute)
    final hasStarted =
        (homeTeam['score'] != null &&
            homeTeam['score'] != 'N/A' &&
            homeTeam['score'] != '') ||
        (awayTeam['score'] != null &&
            awayTeam['score'] != 'N/A' &&
            awayTeam['score'] != '') ||
        currentMinute.isNotEmpty;

    return Column(
      children: [
        // Time and status row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              time,
              style: const TextStyle(color: Colors.white70, fontSize: 14),
            ),
            if (currentMinute.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6.0,
                  vertical: 2.0,
                ),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: Text(
                  currentMinute,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            else if (!hasStarted && time != 'N/A')
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6.0,
                  vertical: 2.0,
                ),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: Text(
                  _getCountdownText(time),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        // Teams and scores row
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Home Team
            Expanded(
              child: Column(
                children: [
                  if (homeTeam['logo'] != null && homeTeam['logo'] != 'N/A')
                    Image.network(
                      homeTeam['logo'],
                      width: 40,
                      height: 40,
                      errorBuilder:
                          (context, error, stackTrace) => const Icon(
                            Icons.sports_soccer,
                            color: Colors.white54,
                            size: 40,
                          ),
                    )
                  else
                    const Icon(
                      Icons.sports_soccer,
                      color: Colors.white54,
                      size: 40,
                    ),
                  const SizedBox(height: 4),
                  Text(
                    homeTeam['name'] ?? 'N/A',
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            // Scores or VS
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                hasStarted
                    ? '${homeTeam['score'] ?? '0'} - ${awayTeam['score'] ?? '0'}'
                    : 'VS',
                style: TextStyle(
                  color: hasStarted ? Colors.white : Colors.orange,
                  fontWeight: FontWeight.bold,
                  fontSize: hasStarted ? 18 : 16,
                ),
              ),
            ),
            // Away Team
            Expanded(
              child: Column(
                children: [
                  if (awayTeam['logo'] != null && awayTeam['logo'] != 'N/A')
                    Image.network(
                      awayTeam['logo'],
                      width: 40,
                      height: 40,
                      errorBuilder:
                          (context, error, stackTrace) => const Icon(
                            Icons.sports_soccer,
                            color: Colors.white54,
                            size: 40,
                          ),
                    )
                  else
                    const Icon(
                      Icons.sports_soccer,
                      color: Colors.white54,
                      size: 40,
                    ),
                  const SizedBox(height: 4),
                  Text(
                    awayTeam['name'] ?? 'N/A',
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getCountdownText(String gameTime) {
    if (gameTime == 'N/A' || gameTime.isEmpty) {
      return 'TBD';
    }

    try {
      // Parse the game time (assuming format like "18:00", "20:30", etc.)
      final timeParts = gameTime.split(':');
      if (timeParts.length != 2) {
        return 'TBD';
      }

      final gameHour = int.parse(timeParts[0]);
      final gameMinute = int.parse(timeParts[1]);

      // Get current time
      final now = DateTime.now();

      // Create game datetime for today
      var gameDateTime = DateTime(
        now.year,
        now.month,
        now.day,
        gameHour,
        gameMinute,
      );

      // If game time has passed today, assume it's tomorrow
      if (gameDateTime.isBefore(now)) {
        gameDateTime = gameDateTime.add(const Duration(days: 1));
      }

      final difference = gameDateTime.difference(now);

      if (difference.isNegative) {
        return 'Started';
      }

      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;

      if (hours > 0) {
        return '${hours}h ${minutes}m';
      } else if (minutes > 0) {
        return '${minutes}m';
      } else {
        return 'Starting';
      }
    } catch (e) {
      return 'TBD';
    }
  }
}
