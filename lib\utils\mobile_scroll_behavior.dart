import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'dart:io';

/// Custom scroll behavior optimized for mobile performance
class MobileOptimizedScrollBehavior extends ScrollBehavior {
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    // Use platform-specific optimized physics
    if (Platform.isAndroid || Platform.isIOS) {
      return const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      );
    }
    return const ClampingScrollPhysics();
  }

  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    // Hide scrollbars on mobile for better performance
    if (Platform.isAndroid || Platform.isIOS) {
      return child;
    }
    return super.buildScrollbar(context, child, details);
  }

  @override
  Widget buildOverscrollIndicator(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    // Disable overscroll glow on mobile for better performance
    if (Platform.isAndroid || Platform.isIOS) {
      return child;
    }
    return super.buildOverscrollIndicator(context, child, details);
  }

  @override
  Set<PointerDeviceKind> get dragDevices => {
    PointerDeviceKind.touch,
    PointerDeviceKind.mouse,
    PointerDeviceKind.stylus,
    PointerDeviceKind.trackpad,
  };

  @override
  MultitouchDragStrategy get multitouchDragStrategy =>
      MultitouchDragStrategy.latestPointer;
}

/// Optimized scroll controller with performance improvements
class OptimizedScrollController extends ScrollController {
  OptimizedScrollController({
    super.initialScrollOffset,
    super.keepScrollOffset,
    super.debugLabel,
  });

  /// Optimized method to check if near bottom with debouncing
  bool isNearBottom({double threshold = 400.0}) {
    if (!hasClients) return false;

    final position = this.position;
    return position.pixels >= (position.maxScrollExtent - threshold);
  }

  /// Smooth scroll to top with optimized animation
  Future<void> animateToTop({
    Duration duration = const Duration(milliseconds: 500),
    Curve curve = Curves.easeOutCubic,
  }) async {
    if (!hasClients) return;

    await animateTo(0.0, duration: duration, curve: curve);
  }
}
