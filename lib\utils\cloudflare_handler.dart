import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'dart:async';

class CloudflareHandler {
  static bool _isCloudflareDetected = false;
  static String _currentUrl = '';
  static Completer<bool>? _verificationCompleter;
  
  static bool get isCloudflareDetected => _isCloudflareDetected;
  static String get currentUrl => _currentUrl;

  /// Detects if the current page is showing Cloudflare protection
  static bool detectCloudflareProtection(String html, String url) {
    _currentUrl = url;
    
    // Common Cloudflare protection indicators
    final cloudflareIndicators = [
      'Checking your browser before accessing',
      'DDoS protection by Cloudflare',
      'Please wait while we check your browser',
      'cf-browser-verification',
      'cf-challenge-form',
      'cloudflare-static',
      'cf-wrapper',
      'cf-error-details',
      'Ray ID:',
      'Cloudflare Ray ID',
      'cf-ray',
      'Please enable JavaScript and cookies to continue',
      'This process is automatic',
      'You will be redirected once the validation is complete',
    ];

    final lowerHtml = html.toLowerCase();
    final lowerUrl = url.toLowerCase();
    
    // Check for Cloudflare indicators in HTML content
    for (final indicator in cloudflareIndicators) {
      if (lowerHtml.contains(indicator.toLowerCase())) {
        _isCloudflareDetected = true;
        if (kDebugMode) {
          debugPrint('🛡️ Cloudflare protection detected: $indicator');
          debugPrint('🌐 URL: $url');
        }
        return true;
      }
    }

    // Check for Cloudflare in URL patterns
    if (lowerUrl.contains('cloudflare') || 
        lowerUrl.contains('cf-ray') ||
        lowerUrl.contains('__cf_chl_jschl_tk__')) {
      _isCloudflareDetected = true;
      if (kDebugMode) {
        debugPrint('🛡️ Cloudflare protection detected in URL: $url');
      }
      return true;
    }

    _isCloudflareDetected = false;
    return false;
  }

  /// Shows a dialog for user to verify Cloudflare protection
  static Future<bool> showCloudflareVerificationDialog(
    BuildContext context,
    String url,
    InAppWebViewController controller,
  ) async {
    if (_verificationCompleter != null && !_verificationCompleter!.isCompleted) {
      return _verificationCompleter!.future;
    }

    _verificationCompleter = Completer<bool>();

    if (kDebugMode) {
      debugPrint('🔐 Showing Cloudflare verification dialog for: $url');
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A1A),
          title: Row(
            children: [
              const Icon(
                Icons.security,
                color: Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                'Cloudflare Protection',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'This site is protected by Cloudflare. Please complete the verification below:',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                height: 400,
                width: double.maxFinite,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: InAppWebView(
                    initialUrlRequest: URLRequest(url: WebUri(url)),
                    initialSettings: InAppWebViewSettings(
                      javaScriptEnabled: true,
                      domStorageEnabled: true,
                      databaseEnabled: true,
                      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    ),
                    onLoadStop: (webController, loadUrl) async {
                      if (kDebugMode) {
                        debugPrint('🔄 Cloudflare verification page loaded: $loadUrl');
                      }
                      
                      // Check if verification is complete
                      final html = await webController.evaluateJavascript(
                        source: 'document.documentElement.outerHTML',
                      );
                      
                      if (html != null && !detectCloudflareProtection(html.toString(), loadUrl.toString())) {
                        if (kDebugMode) {
                          debugPrint('✅ Cloudflare verification completed successfully');
                        }
                        
                        // Copy cookies and session to main controller
                        await _copyCookiesAndSession(webController, controller, loadUrl.toString());
                        
                        Navigator.of(dialogContext).pop();
                        if (!_verificationCompleter!.isCompleted) {
                          _verificationCompleter!.complete(true);
                        }
                      }
                    },
                    onLoadError: (webController, loadUrl, code, message) {
                      if (kDebugMode) {
                        debugPrint('❌ Cloudflare verification error: $message');
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Once verification is complete, the dialog will close automatically and scraping will continue.',
                style: TextStyle(
                  color: Colors.white60,
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                if (!_verificationCompleter!.isCompleted) {
                  _verificationCompleter!.complete(false);
                }
              },
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );

    return _verificationCompleter!.future;
  }

  /// Copies cookies and session data from verification webview to main controller
  static Future<void> _copyCookiesAndSession(
    InAppWebViewController fromController,
    InAppWebViewController toController,
    String url,
  ) async {
    try {
      // Get cookies from verification webview
      final cookieManager = CookieManager.instance();
      final cookies = await cookieManager.getCookies(url: WebUri(url));
      
      if (kDebugMode) {
        debugPrint('🍪 Copying ${cookies.length} cookies to main webview');
      }

      // Set cookies in main webview
      for (final cookie in cookies) {
        await cookieManager.setCookie(
          url: WebUri(url),
          name: cookie.name,
          value: cookie.value,
          domain: cookie.domain,
          path: cookie.path,
          isSecure: cookie.isSecure,
          isHttpOnly: cookie.isHttpOnly,
          sameSite: cookie.sameSite,
        );
      }

      // Copy local storage and session storage
      final localStorage = await fromController.evaluateJavascript(
        source: 'JSON.stringify(localStorage)',
      );
      
      final sessionStorage = await fromController.evaluateJavascript(
        source: 'JSON.stringify(sessionStorage)',
      );

      if (localStorage != null) {
        await toController.evaluateJavascript(
          source: '''
            try {
              const data = $localStorage;
              for (const key in data) {
                localStorage.setItem(key, data[key]);
              }
            } catch (e) {
              console.log('Error copying localStorage:', e);
            }
          ''',
        );
      }

      if (sessionStorage != null) {
        await toController.evaluateJavascript(
          source: '''
            try {
              const data = $sessionStorage;
              for (const key in data) {
                sessionStorage.setItem(key, data[key]);
              }
            } catch (e) {
              console.log('Error copying sessionStorage:', e);
            }
          ''',
        );
      }

      if (kDebugMode) {
        debugPrint('✅ Successfully copied session data to main webview');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error copying session data: $e');
      }
    }
  }

  /// Resets the Cloudflare detection state
  static void reset() {
    _isCloudflareDetected = false;
    _currentUrl = '';
    _verificationCompleter = null;
    if (kDebugMode) {
      debugPrint('🔄 Cloudflare handler state reset');
    }
  }

  /// Injects JavaScript to monitor for Cloudflare challenges
  static String getCloudflareDetectionScript() {
    return '''
      (function() {
        // Monitor for Cloudflare challenge completion
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              const html = document.documentElement.outerHTML;
              if (html.includes('Checking your browser') || 
                  html.includes('DDoS protection by Cloudflare') ||
                  html.includes('cf-browser-verification')) {
                window.flutter_inappwebview.callHandler('cloudflareDetected', html);
              }
            }
          });
        });
        
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
        
        // Initial check
        const html = document.documentElement.outerHTML;
        if (html.includes('Checking your browser') || 
            html.includes('DDoS protection by Cloudflare') ||
            html.includes('cf-browser-verification')) {
          window.flutter_inappwebview.callHandler('cloudflareDetected', html);
        }
      })();
    ''';
  }
}
