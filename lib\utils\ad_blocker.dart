import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class AdBlocker {
  static List<ContentBlocker> _contentBlockers = [];
  static int _blockedAdsCount = 0;
  static bool _isEasyListLoaded = false;
  static String _easyListVersion = '';
  static int _totalRulesLoaded = 0;
  static DateTime? _lastLoadTime;

  static List<ContentBlocker> get contentBlockers => _contentBlockers;
  static int get blockedAdsCount => _blockedAdsCount;
  static bool get isEasyListLoaded => _isEasyListLoaded;
  static String get easyListVersion => _easyListVersion;
  static int get totalRulesLoaded => _totalRulesLoaded;
  static DateTime? get lastLoadTime => _lastLoadTime;

  static void incrementBlockedAdsCount() {
    _blockedAdsCount++;
    if (kDebugMode) {
      debugPrint('🚫 Ad blocked! Total blocked ads: $_blockedAdsCount');
    }
  }

  static void resetBlockedAdsCount() {
    _blockedAdsCount = 0;
    if (kDebugMode) {
      debugPrint('🔄 Blocked ads counter reset');
    }
  }

  static Future<void> loadEasylist() async {
    if (_contentBlockers.isNotEmpty && _isEasyListLoaded) {
      if (kDebugMode) {
        debugPrint(
          '✅ EasyList already loaded with ${_contentBlockers.length} rules',
        );
      }
      return;
    }

    _contentBlockers.clear();
    _isEasyListLoaded = false;
    _totalRulesLoaded = 0;
    try {
      if (kDebugMode) {
        debugPrint('🔄 Loading EasyList from assets/ads/easylist.txt...');
      }

      final String easylistContent = await rootBundle.loadString(
        'assets/ads/easylist.txt',
      );

      if (kDebugMode) {
        debugPrint('✅ EasyList file loaded successfully from assets');
      }

      // Extract version information from EasyList header
      final lines = easylistContent.split('\n');
      for (final line in lines.take(10)) {
        if (line.startsWith('! Version:')) {
          _easyListVersion = line.substring(11).trim();
          if (kDebugMode) {
            debugPrint('📋 EasyList Version: $_easyListVersion');
          }
          break;
        }
      }

      // The resource types provided by the user in the prompt were incomplete.
      // Using a comprehensive list of common resource types for ad blocking.
      final List<ContentBlockerTriggerResourceType> commonResourceTypes = [
        ContentBlockerTriggerResourceType.SCRIPT,
        ContentBlockerTriggerResourceType.IMAGE,
        ContentBlockerTriggerResourceType.STYLE_SHEET,
        ContentBlockerTriggerResourceType.MEDIA,
        ContentBlockerTriggerResourceType.DOCUMENT,
        ContentBlockerTriggerResourceType.FONT,
      ];

      // Add a broad ad-domain blocking rule as requested
      _contentBlockers.add(
        ContentBlocker(
          trigger: ContentBlockerTrigger(
            urlFilter:
                ".*(ads|adserver|advertising|adservice|doubleclick|googleads|googlesyndication|pagead|adnxs|adzerk|openx|rubicon|pubmatic|moat|scorecardresearch|quantserve|adsafeprotected|adtech|adform|adition|adblade|adconion|adinterax|admeta|admeld|advertising|advertise|adview|adwhirl|adworx|aggregateknowledge|amazon-adsystem|aolcloud|appnexus|atdmt|bidswitch|bidvertiser|bluekai|brightroll|casalemedia|chartbeat|chitika|clicksor|clicktale|conversant|coxdigital|creative-serving|crwdcntrl|demdex|dotomi|doubleverify|e-planning|exelator|exponential|eyeota|eyeview|federatedmedia|flashtalking|gemini|gumgum|improvedigital|indexexchange|innovid|integralads|intergi|jivox|juicyads|kargo|kontera|lijit|liveintent|lockerdome|mediamath|media.net|mediamind|millennialmedia|mixpo|mookie1|narrative|nativeroll|outbrain|platform-one|playgroundxyz|polar|powerlinks|pubmatic|pulsepoint|quantcast|reklamstore|revcontent|rhythmone|richrelevance|rubiconproject|runads|scorecardresearch|serving-sys|sharethrough|sonobi|spotx|taboola|teads|telaria|tremorhub|triplelift|truex|tubemogul|undertone|unruly|videology|vindico|weborama|xaxis|yieldlab|yieldmo|yieldone|zedo|adrecover|adblock|adguard|adremover|adstopper|adterminator|adzapper|noads|stopad|ublock).*",
            resourceType: commonResourceTypes,
          ),
          action: ContentBlockerAction(type: ContentBlockerActionType.BLOCK),
        ),
      );

      final List<String> easylistLines = easylistContent.split('\n');
      for (final line in easylistLines) {
        final trimmedLine = line.trim();
        if (trimmedLine.isEmpty ||
            trimmedLine.startsWith('!') || // Comments
            trimmedLine.startsWith('[') || // Section headers
            trimmedLine.startsWith('@@')) {
          // Allowlist rules
          continue;
        }

        String pattern = trimmedLine;

        // Remove EasyList options (e.g., $script, $image, $domain=~example.com)
        int dollarIndex = pattern.indexOf('\$');
        if (dollarIndex != -1) {
          pattern = pattern.substring(0, dollarIndex);
        }

        // Apply EasyList specific conversions first, then escape for regex
        String urlFilterRegex;
        if (pattern.startsWith('||')) {
          // Domain anchor: ||example.com -> matches http(s)://(sub.)example.com
          String domainPattern = pattern.substring(2);
          domainPattern = domainPattern.replaceAll('*', '.*');
          domainPattern = domainPattern.replaceAll(
            '^',
            '[^/]*',
          ); // EasyList ^ means separator
          urlFilterRegex =
              'https?://(?:[^/]+\\.)?${_escapeRegex(domainPattern)}';
        } else if (pattern.startsWith('|')) {
          // Start of address anchor: |http://example.com -> matches http://example.com at the very beginning
          String pathPattern = pattern.substring(1);
          pathPattern = pathPattern.replaceAll('*', '.*');
          pathPattern = pathPattern.replaceAll('^', '[^/]*');
          urlFilterRegex = '^${_escapeRegex(pathPattern)}';
        } else if (pattern.endsWith('|')) {
          // End of address anchor: example.com| -> matches example.com at the very end
          String pathPattern = pattern.substring(0, pattern.length - 1);
          pathPattern = pathPattern.replaceAll('*', '.*');
          pathPattern = pathPattern.replaceAll('^', '[^/]*');
          urlFilterRegex = '${_escapeRegex(pathPattern)}\$';
        } else {
          // Default: match anywhere in the URL
          pattern = pattern.replaceAll('*', '.*');
          pattern = pattern.replaceAll('^', '[^/]*');
          urlFilterRegex = '.*${_escapeRegex(pattern)}.*';
        }

        // Ensure the regex is properly anchored or wildcarded if not already
        if (!urlFilterRegex.startsWith('^') &&
            !urlFilterRegex.startsWith('.*')) {
          urlFilterRegex = '.*$urlFilterRegex';
        }
        if (!urlFilterRegex.endsWith('\$') && !urlFilterRegex.endsWith('.*')) {
          urlFilterRegex = '$urlFilterRegex.*';
        }

        try {
          _contentBlockers.add(
            ContentBlocker(
              trigger: ContentBlockerTrigger(
                urlFilter: urlFilterRegex,
                resourceType: commonResourceTypes,
              ),
              action: ContentBlockerAction(
                type: ContentBlockerActionType.BLOCK,
              ),
            ),
          );
          _totalRulesLoaded++;
        } catch (e) {
          if (kDebugMode) {
            debugPrint(
              '⚠️ Error creating ContentBlocker for pattern "$pattern": $e',
            );
          }
        }
      }

      _isEasyListLoaded = true;
      _lastLoadTime = DateTime.now();

      if (kDebugMode) {
        debugPrint(
          '🎉 Successfully loaded ${_contentBlockers.length} ad blocking rules from EasyList',
        );
        debugPrint('📊 Total rules processed: $_totalRulesLoaded');
        debugPrint('⏰ Load completed at: ${_lastLoadTime.toString()}');
      }
    } catch (e) {
      _isEasyListLoaded = false;
      if (kDebugMode) {
        debugPrint('❌ Error loading EasyList: $e');
      }
    }
  }

  static String _escapeRegex(String input) {
    // Escape special regex characters that are not EasyList wildcards/separators
    // These are handled before calling _escapeRegex
    return input.replaceAllMapped(
      RegExp(
        r'[.+?^${}()|[\]\\]',
      ), // Removed * and ^ from here as they are handled before escaping
      (match) => '\\${match.group(0)}',
    );
  }
}
