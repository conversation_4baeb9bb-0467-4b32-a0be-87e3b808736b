import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Required for SystemChrome
import 'dart:ui'; // Required for ImageFilter
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/utils/ad_blocker.dart';
import 'package:cat_tv/utils/window_manager_windows.dart'; // Import for Windows window management
import 'package:cat_tv/services/webview_utils.dart'; // Import WebViewUtils
import 'package:cat_tv/utils/cloudflare_handler.dart'; // Import CloudflareHandler

class WebViewPage extends StatefulWidget {
  final Channel? channel; // Made nullable
  final String channelUrl;
  final InAppWebViewSettings? initialWebViewSettings; // New parameter
  final bool headless; // New parameter to control visibility

  const WebViewPage({
    super.key,
    this.channel, // Made optional
    required this.channelUrl,
    this.initialWebViewSettings, // New parameter
    this.headless = false, // Default to not headless
  });

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  InAppWebViewController? _webViewController;
  late final TextEditingController _urlController;
  bool _shouldBlockAds = true;
  bool _isDisposing =
      false; // New flag to control widget rendering during disposal
  bool _isFullScreen = false; // New state for fullscreen mode
  bool _isInitialized = false; // New flag to track WebView initialization

  @override
  void initState() {
    super.initState();
    _urlController = TextEditingController(text: widget.channelUrl);
    // Initialize the ad blocker
    WebViewUtils.initAdBlocker();
    // Store the original app bar height
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _urlController.dispose();
    _isDisposing = true; // Set flag to true to stop rendering the WebView

    // Ensure fullscreen mode is exited when the page is disposed
    if (_isFullScreen) {
      _toggleFullscreen();
    }

    // The webview controller is managed by the InAppWebView widget and will be disposed automatically.
    // No need to dispose it manually here.
    _webViewController = null;
    super.dispose();
  }

  void _toggleFullscreen() {
    if (kDebugMode) {
      print("Before fullscreen toggle: ${MediaQuery.of(context).size}");
    }

    setState(() {
      _isFullScreen = !_isFullScreen;
    });

    if (_isFullScreen) {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      if (defaultTargetPlatform == TargetPlatform.windows) {
        WindowManagerWindows.enterFullscreen();
      }
    } else {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: SystemUiOverlay.values,
      );
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
      if (defaultTargetPlatform == TargetPlatform.windows) {
        WindowManagerWindows.exitFullscreen();
      }
    }

    // Add a post-frame callback to get the size after the layout has updated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (kDebugMode) {
        print("After fullscreen toggle: ${MediaQuery.of(context).size}");
      }
    });
  }

  void _loadUrl() {
    final url = _urlController.text.trim();
    if (url.isNotEmpty && _isInitialized && _webViewController != null) {
      _webViewController?.loadUrl(urlRequest: URLRequest(url: WebUri(url)));
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.headless) {
      return SizedBox(
        width: 0,
        height: 0,
        child: InAppWebView(
          initialUrlRequest: URLRequest(url: WebUri(widget.channelUrl)),
          initialSettings:
              widget.initialWebViewSettings ??
              InAppWebViewSettings(
                javaScriptEnabled: true,
                mediaPlaybackRequiresUserGesture: false,
                domStorageEnabled: true,
                databaseEnabled: true,
                javaScriptCanOpenWindowsAutomatically: false,
                supportMultipleWindows: false,
                allowsInlineMediaPlayback: true,
                allowsBackForwardNavigationGestures: false,
                allowsAirPlayForMediaPlayback: false,
                allowFileAccessFromFileURLs: false,
                allowUniversalAccessFromFileURLs: false,
                mixedContentMode: MixedContentMode.MIXED_CONTENT_NEVER_ALLOW,
                contentBlockers:
                    _shouldBlockAds ? AdBlocker.contentBlockers : [],
              ),
          shouldOverrideUrlLoading: (controller, navigationAction) async {
            final url = navigationAction.request.url.toString();
            final currentChannelDomain = WebViewUtils.getBaseDomain(
              widget.channelUrl,
            );
            final targetUrlDomain = WebViewUtils.getBaseDomain(url);

            // Log for all navigation attempts
            if (kDebugMode) {
              debugPrint(
                "shouldOverrideUrlLoading (headless): Attempting to navigate to: $url (Current domain: $currentChannelDomain, Target domain: $targetUrlDomain)",
              );
            }

            // Allow if the domains are the same
            if (currentChannelDomain.isNotEmpty &&
                targetUrlDomain.isNotEmpty &&
                currentChannelDomain == targetUrlDomain) {
              if (kDebugMode) {
                debugPrint(
                  "shouldOverrideUrlLoading (headless): Allowing same-domain navigation: $url",
                );
              }
              return NavigationActionPolicy.ALLOW;
            }

            // Block any cross-domain navigation that is not explicitly allowed by WebViewUtils.shouldBlockUrl
            if (WebViewUtils.shouldBlockUrl(url, widget.channelUrl)) {
              if (kDebugMode) {
                debugPrint(
                  "shouldOverrideUrlLoading (headless): Blocking cross-domain navigation (ad/aggressive redirect): $url",
                );
              }
              return NavigationActionPolicy.CANCEL;
            }

            // Block any cross-domain navigation
            if (kDebugMode) {
              debugPrint(
                "shouldOverrideUrlLoading (headless): Blocking cross-domain navigation: $url",
              );
            }
            return NavigationActionPolicy.CANCEL;
          },
          onCreateWindow: (controller, createWindowRequest) async {
            final newUrl = createWindowRequest.request.url.toString();
            final currentChannelDomain = WebViewUtils.getBaseDomain(
              widget.channelUrl,
            );
            final newWindowDomain = WebViewUtils.getBaseDomain(newUrl);

            if (kDebugMode) {
              print(
                "Attempting to open new window: $newUrl (Current domain: $currentChannelDomain, New window domain: $newWindowDomain)",
              );
            }

            // Block any cross-domain pop-ups
            if (kDebugMode) {
              print("Blocking popup: $newUrl");
            }
            return false;
          },
          onWebViewCreated: (controller) {
            _webViewController = controller;
            setState(() {
              _isInitialized = true;
            });
            controller.addJavaScriptHandler(
              handlerName: 'scrapeHandler',
              callback: (args) {
                if (kDebugMode) {
                  print('Received scraped data: $args');
                }
                if (args.isNotEmpty && args[0] is List) {
                  WebViewUtils.saveScrapedData(args[0] as List<dynamic>);
                }
              },
            );
            controller.addJavaScriptHandler(
              handlerName: 'siItemScrapeHandler',
              callback: (args) {
                if (kDebugMode) {
                  print('SI Item Scraped Data: $args');
                }
                // You can add further processing here if needed, e.g., saving to a file
                if (!_isDisposing && _webViewController != null) {
                  _webViewController!.evaluateJavascript(
                    source: '''
                      console.log('Attempting to find and click start button...');
                      var startButton = document.querySelector('.xgplayer-start');
                      if (startButton) {
                        console.log('Start button found, attempting to click.');
                        startButton.click();
                        console.log('Start button clicked.');
                      } else {
                        console.log('Start button not found.');
                      }
                    ''',
                  );
                }
              },
            );
            controller.addJavaScriptHandler(
              handlerName: 'serversAndSiItemsScrapeHandler',
              callback: (args) {
                if (kDebugMode) {
                  print('Servers and SI Items Scraped Data: $args');
                  // Iterate through the scraped data and print as requested
                  if (args.isNotEmpty && args[0] is List) {
                    final List<dynamic> scrapedData = args[0];
                    for (var serverItemData in scrapedData) {
                      print(
                        'Scraped server-item: ${serverItemData['outerHTML']}',
                      );
                      for (var siItemData in serverItemData['siItems']) {
                        print('  Scraped si-item: ${siItemData['outerHTML']}');
                      }
                    }
                  }
                }
              },
            );
            controller.addJavaScriptHandler(
              handlerName: 'fullHtmlScrapeHandler',
              callback: (args) {
                if (kDebugMode) {
                  print('Received full HTML data. Saving to cache...');
                }
                if (args.isNotEmpty && args[0] is String) {
                  WebViewUtils.saveHtmlToCache(
                    args[0] as String,
                    'game_page.html',
                  );
                }
              },
            );
          },
          onLoadStart: (controller, url) {
            if (kDebugMode) {
              debugPrint(
                "onLoadStart (headless): WebView started loading: $url",
              );
            }
            final currentChannelDomain = WebViewUtils.getBaseDomain(
              widget.channelUrl,
            );
            final targetUrlDomain = WebViewUtils.getBaseDomain(url.toString());

            // Log for all load start attempts
            if (kDebugMode) {
              debugPrint(
                "onLoadStart (headless): Attempting to load: $url (Current domain: $currentChannelDomain, Target domain: $targetUrlDomain)",
              );
            }

            // If it's a cross-domain navigation that is blocked by WebViewUtils, stop loading immediately.
            if (currentChannelDomain.isNotEmpty &&
                targetUrlDomain.isNotEmpty &&
                currentChannelDomain != targetUrlDomain &&
                WebViewUtils.shouldBlockUrl(
                  url.toString(),
                  widget.channelUrl,
                )) {
              if (kDebugMode) {
                debugPrint(
                  "onLoadStart (headless): Stopping cross-domain load (ad/aggressive redirect): $url",
                );
              }
              controller.stopLoading();
            } else if (currentChannelDomain.isNotEmpty &&
                targetUrlDomain.isNotEmpty &&
                currentChannelDomain != targetUrlDomain) {
              if (kDebugMode) {
                debugPrint(
                  "onLoadStart (headless): Allowing cross-domain load (not blocked by rules): $url",
                );
              }
            } else {
              if (kDebugMode) {
                debugPrint(
                  "onLoadStart (headless): Allowing same-domain load: $url",
                );
              }
            }
          },
          onLoadStop: (controller, url) async {
            if (kDebugMode) {
              print("WebView finished loading: $url");
            }
            await WebViewUtils.injectAdBlockingScript(controller);
            await controller.evaluateJavascript(
              source: WebViewUtils.getScrapingScript(),
            ); // Call general scraping function

            // Also call the SI Item scraping script
            await controller.evaluateJavascript(
              source: WebViewUtils.getSiItemScrapingScript(),
            );

            // Call the full HTML scraping script
            await controller.evaluateJavascript(
              source: WebViewUtils.getPageHtmlScrapingScript(),
            );

            // If in headless mode, pop the route after scraping is done
            if (widget.headless) {
              if (mounted) {
                Navigator.of(context).pop();
              }
            } else {
              // Add a delay to ensure the page is fully rendered
              await Future.delayed(const Duration(seconds: 7));
              if (!_isDisposing && _webViewController != null) {
                for (int i = 0; i < 10; i++) {
                  _webViewController!.evaluateJavascript(
                    source: '''
                      console.log('Attempting to find and click start button (Attempt ${i + 1}/10)...');
                      var startButton = document.querySelector('.xgplayer-start');
                      if (startButton) {
                        console.log('Start button found, attempting to click.');
                        startButton.click();
                        console.log('Start button clicked.');
                      } else {
                        console.log('Start button not found.');
                      }
                    ''',
                  );
                  await Future.delayed(const Duration(seconds: 1));
                }
              }
            }
          },
          onReceivedError: (controller, request, error) {
            if (kDebugMode) {
              print(
                "Error loading ${request.url}: ${error.description} (Code: ${error.type})",
              );
            }
          },
          onConsoleMessage: (controller, consoleMessage) {
            if (kDebugMode) {
              print("Console Message: ${consoleMessage.message}");
            }
          },
        ),
      );
    }

    return KeyboardListener(
      focusNode: FocusNode(),
      autofocus: true,
      onKeyEvent: (KeyEvent event) {
        if (event is KeyDownEvent &&
            event.logicalKey == LogicalKeyboardKey.escape) {
          if (_isFullScreen) {
            _toggleFullscreen();
          }
        }
      },
      child: PopScope(
        canPop: !_isFullScreen, // Allow pop if not fullscreen
        onPopInvokedWithResult: (didPop, result) {
          if (_isFullScreen) {
            _toggleFullscreen();
          }
        },
        child: Scaffold(
          extendBodyBehindAppBar: true,
          backgroundColor: Colors.black,
          appBar:
              _isFullScreen
                  ? null // Hide AppBar in fullscreen
                  : AppBar(
                    title: Text(
                      widget.channel?.name ??
                          'Web View', // Handle nullable channel
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    centerTitle: false, // Changed to false to align title left
                    flexibleSpace: ClipRRect(
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                        child: Container(
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color.fromARGB(255, 50, 0, 50), // Dark purple
                                Color.fromARGB(255, 20, 20, 20), // Dark grey
                              ],
                            ),
                            border: Border(
                              bottom: BorderSide(
                                color: Colors.white10,
                                width: 0.5,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () async {
                        if (_webViewController != null &&
                            await _webViewController!.canGoBack()) {
                          _webViewController!.goBack();
                        } else {
                          Navigator.of(context).pop();
                        }
                      },
                      tooltip: 'Back to Channels',
                    ),
                    actions: [
                      IconButton(
                        icon: Icon(
                          _shouldBlockAds
                              ? Icons.shield
                              : Icons.shield_outlined,
                          color: Colors.white,
                          size:
                              28.0, // Making it slightly larger for "thick" effect
                        ),
                        onPressed: () {
                          setState(() {
                            _shouldBlockAds = !_shouldBlockAds;
                          });
                          _loadUrl();
                        },
                        tooltip:
                            _shouldBlockAds
                                ? 'Disable Ad Blocking'
                                : 'Enable Ad Blocking',
                      ),
                      IconButton(
                        icon: Icon(
                          _isFullScreen
                              ? Icons.fullscreen_exit
                              : Icons.fullscreen,
                          color: Colors.white,
                          size:
                              28.0, // Making it slightly larger for "thick" effect
                        ),
                        onPressed: _toggleFullscreen,
                        tooltip:
                            _isFullScreen ? 'Exit Fullscreen' : 'Fullscreen',
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.refresh,
                          color: Colors.white,
                          size:
                              28.0, // Making it slightly larger for "thick" effect
                        ),
                        onPressed: _loadUrl,
                        tooltip: 'Refresh', // Added tooltip
                      ),
                    ],
                  ),
          body: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color.fromARGB(255, 20, 20, 20),
                      Color.fromARGB(255, 50, 0, 50),
                    ],
                  ),
                ),
                child:
                    _isDisposing
                        ? const SizedBox.shrink()
                        : SafeArea(
                          child: Center(
                            child: InAppWebView(
                              initialUrlRequest: URLRequest(
                                url: WebUri(widget.channelUrl),
                              ),
                              initialSettings:
                                  widget.initialWebViewSettings ??
                                  InAppWebViewSettings(
                                    javaScriptEnabled: true,
                                    mediaPlaybackRequiresUserGesture: false,
                                    domStorageEnabled: true,
                                    databaseEnabled: true,
                                    javaScriptCanOpenWindowsAutomatically:
                                        false,
                                    supportMultipleWindows: false,
                                    allowsInlineMediaPlayback: true,
                                    allowsBackForwardNavigationGestures: false,
                                    allowsAirPlayForMediaPlayback: false,
                                    allowFileAccessFromFileURLs: false,
                                    allowUniversalAccessFromFileURLs: false,
                                    mixedContentMode:
                                        MixedContentMode
                                            .MIXED_CONTENT_NEVER_ALLOW,
                                    contentBlockers:
                                        _shouldBlockAds
                                            ? AdBlocker.contentBlockers
                                            : [],
                                  ),
                              shouldOverrideUrlLoading: (
                                controller,
                                navigationAction,
                              ) async {
                                final url =
                                    navigationAction.request.url.toString();
                                final currentChannelDomain =
                                    WebViewUtils.getBaseDomain(
                                      widget.channelUrl,
                                    );
                                final targetUrlDomain =
                                    WebViewUtils.getBaseDomain(url);

                                // Log for all navigation attempts
                                if (kDebugMode) {
                                  debugPrint(
                                    "shouldOverrideUrlLoading (non-headless): Attempting to navigate to: $url (Current domain: $currentChannelDomain, Target domain: $targetUrlDomain)",
                                  );
                                }

                                // Allow if the domains are the same
                                if (currentChannelDomain.isNotEmpty &&
                                    targetUrlDomain.isNotEmpty &&
                                    currentChannelDomain == targetUrlDomain) {
                                  if (kDebugMode) {
                                    debugPrint(
                                      "shouldOverrideUrlLoading (non-headless): Allowing same-domain navigation: $url",
                                    );
                                  }
                                  return NavigationActionPolicy.ALLOW;
                                }

                                // Block any cross-domain navigation that is not explicitly allowed by WebViewUtils.shouldBlockUrl
                                if (WebViewUtils.shouldBlockUrl(
                                  url,
                                  widget.channelUrl,
                                )) {
                                  if (kDebugMode) {
                                    debugPrint(
                                      "shouldOverrideUrlLoading (non-headless): Blocking cross-domain navigation (ad/aggressive redirect): $url",
                                    );
                                  }
                                  return NavigationActionPolicy.CANCEL;
                                }

                                // Block any cross-domain navigation
                                if (kDebugMode) {
                                  debugPrint(
                                    "shouldOverrideUrlLoading (non-headless): Blocking cross-domain navigation: $url",
                                  );
                                }
                                return NavigationActionPolicy.CANCEL;
                              },
                              onCreateWindow: (
                                controller,
                                createWindowRequest,
                              ) async {
                                final newUrl =
                                    createWindowRequest.request.url.toString();
                                final currentChannelDomain =
                                    WebViewUtils.getBaseDomain(
                                      widget.channelUrl,
                                    );
                                final newWindowDomain =
                                    WebViewUtils.getBaseDomain(newUrl);

                                if (kDebugMode) {
                                  print(
                                    "Attempting to open new window: $newUrl (Current domain: $currentChannelDomain, New window domain: $newWindowDomain)",
                                  );
                                }

                                // Block any cross-domain pop-ups
                                if (kDebugMode) {
                                  print("Blocking popup: $newUrl");
                                }
                                return false;
                              },
                              onWebViewCreated: (controller) {
                                _webViewController = controller;
                                setState(() {
                                  _isInitialized = true;
                                });
                                controller.addJavaScriptHandler(
                                  handlerName: 'scrapeHandler',
                                  callback: (args) {
                                    if (kDebugMode) {
                                      print('Received scraped data: $args');
                                    }
                                    // Do NOT save scraped data to fixtures.json when in non-headless mode (i.e., opening a game link)
                                    // This handler is primarily for the main fixtures list scraping in headless mode.
                                  },
                                );
                                controller.addJavaScriptHandler(
                                  handlerName: 'siItemScrapeHandler',
                                  callback: (args) {
                                    if (kDebugMode) {
                                      print('SI Item Scraped Data: $args');
                                    }
                                    // You can add further processing here if needed
                                    if (!_isDisposing &&
                                        _webViewController != null) {
                                      _webViewController!.evaluateJavascript(
                                        source: '''
                                          console.log('Attempting to find and click start button...');
                                          var startButton = document.querySelector('.xgplayer-start');
                                          if (startButton) {
                                            console.log('Start button found, attempting to click.');
                                            startButton.click();
                                            console.log('Start button clicked.');
                                          } else {
                                            console.log('Start button not found.');
                                          }
                                        ''',
                                      );
                                    }
                                  },
                                );
                                controller.addJavaScriptHandler(
                                  handlerName: 'serversAndSiItemsScrapeHandler',
                                  callback: (args) {
                                    if (kDebugMode) {
                                      print(
                                        'Servers and SI Items Scraped Data: $args',
                                      );
                                      // Iterate through the scraped data and print as requested
                                      if (args.isNotEmpty && args[0] is List) {
                                        final List<dynamic> scrapedData =
                                            args[0];
                                        for (var serverItemData
                                            in scrapedData) {
                                          print(
                                            'Scraped server-item: ${serverItemData['outerHTML']}',
                                          );
                                          for (var siItemData
                                              in serverItemData['siItems']) {
                                            print(
                                              '  Scraped si-item: ${siItemData['outerHTML']}',
                                            );
                                          }
                                        }
                                      }
                                    }
                                  },
                                );
                                controller.addJavaScriptHandler(
                                  handlerName: 'fullHtmlScrapeHandler',
                                  callback: (args) {
                                    if (kDebugMode) {
                                      print(
                                        'Received full HTML data. Saving to cache...',
                                      );
                                    }
                                    if (args.isNotEmpty && args[0] is String) {
                                      WebViewUtils.saveHtmlToCache(
                                        args[0] as String,
                                        'game_page.html',
                                      );
                                    }
                                  },
                                );
                              },
                              onLoadStart: (controller, url) {
                                if (kDebugMode) {
                                  debugPrint(
                                    "onLoadStart (non-headless): WebView started loading: $url",
                                  );
                                }
                                final currentChannelDomain =
                                    WebViewUtils.getBaseDomain(
                                      widget.channelUrl,
                                    );
                                final targetUrlDomain =
                                    WebViewUtils.getBaseDomain(url.toString());

                                // Log for all load start attempts
                                if (kDebugMode) {
                                  debugPrint(
                                    "onLoadStart (non-headless): Attempting to load: $url (Current domain: $currentChannelDomain, Target domain: $targetUrlDomain)",
                                  );
                                }

                                // If it's a cross-domain navigation that is blocked by WebViewUtils, stop loading immediately.
                                if (currentChannelDomain.isNotEmpty &&
                                    targetUrlDomain.isNotEmpty &&
                                    currentChannelDomain != targetUrlDomain &&
                                    WebViewUtils.shouldBlockUrl(
                                      url.toString(),
                                      widget.channelUrl,
                                    )) {
                                  if (kDebugMode) {
                                    debugPrint(
                                      "onLoadStart (non-headless): Stopping cross-domain load (ad/aggressive redirect): $url",
                                    );
                                  }
                                  controller.stopLoading();
                                } else if (currentChannelDomain.isNotEmpty &&
                                    targetUrlDomain.isNotEmpty &&
                                    currentChannelDomain != targetUrlDomain) {
                                  if (kDebugMode) {
                                    debugPrint(
                                      "onLoadStart (non-headless): Allowing cross-domain load (not blocked by rules): $url",
                                    );
                                  }
                                } else {
                                  if (kDebugMode) {
                                    debugPrint(
                                      "onLoadStart (non-headless): Allowing same-domain load: $url",
                                    );
                                  }
                                }
                              },
                              onLoadStop: (controller, url) async {
                                if (kDebugMode) {
                                  print("WebView finished loading: $url");
                                }
                                await WebViewUtils.injectAdBlockingScript(
                                  controller,
                                );
                                await controller.evaluateJavascript(
                                  source: WebViewUtils.getScrapingScript(),
                                ); // Call general scraping function

                                // Also call the SI Item scraping script
                                await controller.evaluateJavascript(
                                  source:
                                      WebViewUtils.getSiItemScrapingScript(),
                                );
                                // Call the new scraping script for servers and si-items
                                await controller.evaluateJavascript(
                                  source:
                                      WebViewUtils.getServersAndSiItemsScrapingScript(),
                                );
                                // Call the full HTML scraping script
                                await controller.evaluateJavascript(
                                  source:
                                      WebViewUtils.getPageHtmlScrapingScript(),
                                );

                                // Add a delay to ensure the page is fully rendered
                                await Future.delayed(
                                  const Duration(seconds: 7),
                                );
                                if (!_isDisposing &&
                                    _webViewController != null) {
                                  for (int i = 0; i < 10; i++) {
                                    _webViewController!.evaluateJavascript(
                                      source: '''
                                        console.log('Attempting to find and click start button (Attempt ${i + 1}/10)...');
                                        var startButton = document.querySelector('.xgplayer-start');
                                        if (startButton) {
                                          console.log('Start button found, attempting to click.');
                                          startButton.click();
                                          console.log('Start button clicked.');
                                        } else {
                                          console.log('Start button not found.');
                                        }
                                      ''',
                                    );
                                    await Future.delayed(
                                      const Duration(seconds: 1),
                                    );
                                  }
                                }
                              },
                              onReceivedError: (controller, request, error) {
                                if (kDebugMode) {
                                  print(
                                    "Error loading ${request.url}: ${error.description} (Code: ${error.type})",
                                  );
                                }
                              },
                              onConsoleMessage: (controller, consoleMessage) {
                                if (kDebugMode) {
                                  print(
                                    "Console Message: ${consoleMessage.message}",
                                  );
                                }
                              },
                            ),
                          ),
                        ),
              ),
              // Escape button for fullscreen mode
              Offstage(
                offstage: !_isFullScreen,
                child: Align(
                  alignment: Alignment.topLeft,
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top + 10,
                      left: 10,
                    ),
                    child: FloatingActionButton(
                      mini: true,
                      backgroundColor: Colors.black54,
                      onPressed: _toggleFullscreen,
                      child: const Icon(
                        Icons.fullscreen_exit,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
