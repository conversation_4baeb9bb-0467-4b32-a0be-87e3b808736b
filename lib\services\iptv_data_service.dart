import 'package:shared_preferences/shared_preferences.dart';
import 'package:cat_tv/services/shared_preferences_manager.dart'; // Import the new manager

class IptvDataService {
  static const String _activationTimeKey = 'flutter.iptv_activation_time';
  static const String _expirationTimeKey = 'flutter.iptv_expiration_time';
  static const String _serverStatusKey = 'flutter.server_status';

  final SharedPreferencesManager _prefsManager = SharedPreferencesManager();

  Future<void> saveIptvData(Map<String, dynamic> data) async {
    await _prefsManager.updateIptvData(data);
    await _prefsManager.setServerStatus('updated');
  }

  Future<Map<String, dynamic>?> loadIptvData() async {
    return await _prefsManager.getIptvData();
  }

  Future<DateTime?> getActivationTime() async {
    final prefs = await SharedPreferences.getInstance();
    final String? timestampString = prefs.getString(_activationTimeKey);
    if (timestampString == null) {
      return null;
    }
    return DateTime.tryParse(timestampString);
  }

  Future<DateTime?> getExpirationTime() async {
    final prefs = await SharedPreferences.getInstance();
    final String? timestampString = prefs.getString(_expirationTimeKey);
    if (timestampString == null) {
      return null;
    }
    return DateTime.tryParse(timestampString);
  }

  Future<String?> getServerStatus() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_serverStatusKey);
  }
}
