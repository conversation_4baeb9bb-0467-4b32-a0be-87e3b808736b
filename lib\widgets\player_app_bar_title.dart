import 'package:flutter/material.dart';
import 'package:cat_tv/models/channel.dart';

class PlayerAppBarTitle extends StatelessWidget {
  final Channel channel;
  final List<Map<String, dynamic>> channelSources;
  final int currentSourceIndex;
  final Function(int) onLoadSource;

  const PlayerAppBarTitle({
    super.key,
    required this.channel,
    required this.channelSources,
    required this.currentSourceIndex,
    required this.onLoadSource,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          channel.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: LayoutBuilder(
            builder: (context, constraints) {
              final double availableWidth = constraints.maxWidth;
              const double buttonWidth =
                  80.0; // Approximate width of a source button
              const double buttonPadding = 8.0; // Horizontal padding
              final int maxButtons =
                  (availableWidth / (buttonWidth + buttonPadding)).floor();

              if (MediaQuery.of(context).size.width < 600) {
                // Mobile view: Dropdown
                return DropdownButtonHideUnderline(
                  child: DropdownButton<int>(
                    value: currentSourceIndex,
                    dropdownColor: Theme.of(
                      context,
                    ).colorScheme.primary.withOpacity(0.8),
                    icon: const Icon(
                      Icons.arrow_drop_down,
                      color: Colors.white,
                    ),
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                    onChanged: (int? newIndex) {
                      if (newIndex != null) {
                        onLoadSource(newIndex);
                      }
                    },
                    items:
                        channelSources.asMap().entries.map((entry) {
                          int index = entry.key;
                          return DropdownMenuItem<int>(
                            value: index,
                            child: Text(
                              'Source ${index + 1}',
                              style: TextStyle(
                                color:
                                    currentSourceIndex == index
                                        ? Theme.of(
                                          context,
                                        ).colorScheme.secondary
                                        : Colors.white,
                                fontWeight:
                                    currentSourceIndex == index
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                );
              } else {
                // PC view: Row of buttons with overflow handling
                final List<Widget> visibleButtons = [];
                final List<Map<String, dynamic>> hiddenSources = [];

                for (int i = 0; i < channelSources.length; i++) {
                  if (i < maxButtons - 1 ||
                      channelSources.length <= maxButtons) {
                    visibleButtons.add(
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0),
                        child: ElevatedButton(
                          onPressed: () {
                            onLoadSource(i);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                currentSourceIndex == i
                                    ? Theme.of(context).colorScheme.secondary
                                    : Theme.of(
                                      context,
                                    ).colorScheme.primary.withOpacity(0.2),
                            foregroundColor:
                                Theme.of(context).colorScheme.onPrimary,
                            minimumSize: Size.zero,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(
                                color: Theme.of(
                                  context,
                                ).colorScheme.onPrimary.withOpacity(0.3),
                              ),
                            ),
                          ),
                          child: Text(
                            'Source ${i + 1}',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                      ),
                    );
                  } else {
                    hiddenSources.add(channelSources[i]);
                  }
                }

                if (hiddenSources.isNotEmpty) {
                  visibleButtons.add(
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4.0),
                      child: ElevatedButton(
                        onPressed: () {
                          // Show missing sources in a dropdown/popup
                          showMenu<int>(
                            context: context,
                            position: RelativeRect.fromLTRB(
                              MediaQuery.of(context).size.width -
                                  100, // Adjust position as needed
                              kToolbarHeight,
                              0,
                              0,
                            ),
                            items:
                                hiddenSources.asMap().entries.map((entry) {
                                  int originalIndex =
                                      maxButtons - 1 + entry.key;
                                  return PopupMenuItem<int>(
                                    value: originalIndex,
                                    child: Text('Source ${originalIndex + 1}'),
                                  );
                                }).toList(),
                          ).then((value) {
                            if (value != null) {
                              onLoadSource(value);
                            }
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(
                            context,
                          ).colorScheme.primary.withOpacity(0.2),
                          foregroundColor:
                              Theme.of(context).colorScheme.onPrimary,
                          minimumSize: Size.zero,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(
                              color: Theme.of(
                                context,
                              ).colorScheme.onPrimary.withOpacity(0.3),
                            ),
                          ),
                        ),
                        child: const Text(
                          '...',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                    ),
                  );
                }

                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: visibleButtons,
                );
              }
            },
          ),
        ),
      ],
    );
  }
}
