import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:cat_tv/utils/ad_blocker.dart';
import 'package:cat_tv/utils/cloudflare_handler.dart';

class WebViewUtils {
  static Future<void> initAdBlocker() async {
    if (kDebugMode) {
      debugPrint('🚀 Initializing ad blocker...');
    }

    await AdBlocker.loadEasylist();

    if (kDebugMode) {
      debugPrint('📊 Ad Blocker Status:');
      debugPrint('  - EasyList loaded: ${AdBlocker.isEasyListLoaded}');
      debugPrint('  - Version: ${AdBlocker.easyListVersion}');
      debugPrint('  - Total rules: ${AdBlocker.totalRulesLoaded}');
      debugPrint('  - Load time: ${AdBlocker.lastLoadTime}');
    }
  }

  static String getBaseDomain(String url) {
    try {
      final uri = Uri.parse(url);
      // Return host, removing 'www.' if present
      return uri.host.startsWith('www.') ? uri.host.substring(4) : uri.host;
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing URL for domain: $url, Error: $e');
      }
      return ''; // Return empty string on error
    }
  }

  static Future<void> injectAdBlockingScript(
    InAppWebViewController controller,
  ) async {
    if (kDebugMode) {
      debugPrint(
        '🛡️ Injecting ad blocking script with ${AdBlocker.contentBlockers.length} rules',
      );
    }

    // Apply content blocker rules from EasyList using the new API
    await controller.setSettings(
      settings: InAppWebViewSettings(
        contentBlockers: AdBlocker.contentBlockers,
      ),
    );

    final script = '''
      (function() {
        console.log('🛡️ Enhanced ad blocking script injected');
        console.log('📊 Content blockers active: ${AdBlocker.contentBlockers.length}');

        // Track blocked requests
        let blockedCount = 0;

        // Block aggressive redirects
        const originalOpen = window.open;
        window.open = function(url, name, specs) {
          blockedCount++;
          console.log('🚫 Blocked window.open attempt #' + blockedCount + ':', url);
          window.flutter_inappwebview.callHandler('adBlocked', {
            type: 'popup',
            url: url,
            count: blockedCount
          });
          return null;
        };

        // Block location changes
        const originalAssign = window.location.assign;
        const originalReplace = window.location.replace;
        const originalReload = window.location.reload;

        window.location.assign = function(url) {
          if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
            blockedCount++;
            console.log('🚫 Blocked location.assign #' + blockedCount + ':', url);
            window.flutter_inappwebview.callHandler('adBlocked', {
              type: 'redirect',
              url: url,
              count: blockedCount
            });
            return;
          }
          return originalAssign.call(this, url);
        };

        window.location.replace = function(url) {
          if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
            blockedCount++;
            console.log('🚫 Blocked location.replace #' + blockedCount + ':', url);
            window.flutter_inappwebview.callHandler('adBlocked', {
              type: 'redirect',
              url: url,
              count: blockedCount
            });
            return;
          }
          return originalReplace.call(this, url);
        };

        // Block document.location changes (only if not already defined)
        try {
          let originalLocation = document.location;
          Object.defineProperty(document, 'location', {
            get: function() { return originalLocation; },
            set: function(url) {
              if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
                console.log('Blocked document.location change:', url);
                return;
              }
              originalLocation = url;
            },
            configurable: true
          });
        } catch (e) {
          console.log('Could not redefine document.location (already defined):', e.message);
        }

        // Block aggressive click handlers (but preserve player controls)
        document.addEventListener('click', function(e) {
          const target = e.target;
          const href = target.href || target.getAttribute('href');

          // Check if this is a player control element
          const isPlayerControl = target.closest('video') ||
                                target.closest('[class*="player"]') ||
                                target.closest('[class*="video"]') ||
                                target.closest('[class*="control"]') ||
                                target.closest('[class*="quality"]') ||
                                target.closest('[class*="menu"]') ||
                                target.closest('[class*="button"]') ||
                                target.closest('[role="button"]') ||
                                target.hasAttribute('aria-label') ||
                                target.tagName === 'BUTTON';

          if (!isPlayerControl && href && (href.includes('opera') || href.includes('download') || href.includes('install'))) {
            console.log('Blocked click on suspicious link:', href);
            e.preventDefault();
            e.stopPropagation();
            return false;
          }
        }, true);

        // Block form submissions to suspicious URLs
        document.addEventListener('submit', function(e) {
          const action = e.target.action;
          if (action && (action.includes('opera') || action.includes('download') || action.includes('install'))) {
            console.log('Blocked form submission to:', action);
            e.preventDefault();
            e.stopPropagation();
            return false;
          }
        }, true);

        // Remove suspicious elements (but preserve video player controls)
        function removeSuspiciousElements() {
          const suspiciousSelectors = [
            'a[href*="opera"]:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            'a[href*="download"]:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            'a[href*="install"]:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            'iframe[src*="opera"]:not([class*="player"]):not([class*="video"])',
            'iframe[src*="download"]:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            'iframe[src*="install"]:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            '.popup:not([class*="player"]):not([class*="control"]):not([class*="video"]):not([class*="quality"]):not([class*="menu"])',
            '.interstitial:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            '[id*="popup"]:not([id*="player"]):not([id*="control"]):not([id*="video"]):not([id*="quality"]):not([id*="menu"])',
            '[class*="popup"]:not([class*="player"]):not([class*="control"]):not([class*="video"]):not([class*="quality"]):not([class*="menu"])'
          ];

          suspiciousSelectors.forEach(selector => {
            try {
              const elements = document.querySelectorAll(selector);
              elements.forEach(el => {
                // Additional check to avoid removing video player elements
                const isPlayerElement = el.closest('video') ||
                                      el.closest('[class*="player"]') ||
                                      el.closest('[class*="video"]') ||
                                      el.closest('[class*="control"]') ||
                                      el.closest('[class*="quality"]') ||
                                      el.closest('[class*="menu"]') ||
                                      el.querySelector('video') ||
                                      el.querySelector('[class*="player"]') ||
                                      el.querySelector('[class*="video"]');

                if (!isPlayerElement) {
                  console.log('Removing suspicious element:', el);
                  el.remove();
                }
              });
            } catch (e) {
              // Ignore errors
            }
          });
        }

        // Run immediately and on DOM changes
        removeSuspiciousElements();

        // Watch for new elements
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              removeSuspiciousElements();
            }
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });

        // Cloudflare detection
        function checkForCloudflare() {
          const html = document.documentElement.outerHTML;
          const cloudflareIndicators = [
            'Checking your browser before accessing',
            'DDoS protection by Cloudflare',
            'Please wait while we check your browser',
            'cf-browser-verification',
            'cf-challenge-form',
            'cloudflare-static',
            'cf-wrapper',
            'cf-error-details',
            'Ray ID:',
            'Cloudflare Ray ID',
            'cf-ray'
          ];

          for (const indicator of cloudflareIndicators) {
            if (html.includes(indicator)) {
              console.log('🛡️ Cloudflare protection detected:', indicator);
              window.flutter_inappwebview.callHandler('cloudflareDetected', {
                indicator: indicator,
                url: window.location.href,
                html: html
              });
              return true;
            }
          }
          return false;
        }

        // Initial Cloudflare check
        checkForCloudflare();

        // Monitor for Cloudflare challenges
        const cloudflareObserver = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              checkForCloudflare();
            }
          });
        });

        cloudflareObserver.observe(document.body, {
          childList: true,
          subtree: true
        });

        console.log('🎉 Enhanced ad blocking script fully loaded with Cloudflare detection');
        console.log('📊 Total blocked requests will be tracked and reported');
      })();
    ''';

    try {
      await controller.evaluateJavascript(source: script);
      if (kDebugMode) {
        debugPrint('✅ Enhanced ad blocking script injected successfully');
      }

      // Add JavaScript handlers for ad blocking events
      controller.addJavaScriptHandler(
        handlerName: 'adBlocked',
        callback: (args) {
          if (args.isNotEmpty && args[0] is Map) {
            final data = Map<String, dynamic>.from(args[0]);
            AdBlocker.incrementBlockedAdsCount();
            if (kDebugMode) {
              debugPrint(
                '🚫 Ad blocked - Type: ${data['type']}, URL: ${data['url']}',
              );
            }
          }
        },
      );

      controller.addJavaScriptHandler(
        handlerName: 'cloudflareDetected',
        callback: (args) {
          if (args.isNotEmpty && args[0] is Map) {
            final data = Map<String, dynamic>.from(args[0]);
            if (kDebugMode) {
              debugPrint(
                '🛡️ Cloudflare detected - Indicator: ${data['indicator']}',
              );
              debugPrint('🌐 URL: ${data['url']}');
            }
            // The Cloudflare handler will be used in the webview pages
          }
        },
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error injecting enhanced ad blocking script: $e');
      }
    }
  }

  static String getScrapingScript() {
    const script = '''
      (function() {
        const result = [];
        const competitionsMap = new Map(); // To group games by competition
        const matchListDiv = document.querySelector('.match-list');

        if (matchListDiv) {
          const itemWraps = matchListDiv.querySelectorAll('.item-wrap');
          console.log('Found', itemWraps.length, 'item-wrap elements');

          itemWraps.forEach((itemWrap, index) => {
            console.log('Processing item-wrap', index + 1);

            // Get the first anchor tag (competition info)
            const competitionAnchor = itemWrap.querySelector('a._6xejLK');
            if (!competitionAnchor) {
              console.log('No competition anchor found in item-wrap', index + 1);
              return;
            }

            // Get competition details
            const competitionDiv = competitionAnchor.querySelector('.gJrz5O');
            if (!competitionDiv) {
              console.log('No competition div found in item-wrap', index + 1);
              return;
            }

            // Get country and competition name spans
            const countrySpan = competitionDiv.querySelector('span:first-of-type');
            const competitionNameSpan = competitionDiv.querySelector('span.Bld2gZ');

            if (!competitionNameSpan) {
              console.log('No competition name span found in item-wrap', index + 1);
              return;
            }

            const competitionName = competitionNameSpan.textContent.trim();

            // Check if country span has display:none style (skip condition)
            const countryText = countrySpan ? countrySpan.textContent.trim() : '';
            const isCountryHidden = countrySpan && countrySpan.style.display === 'none';

            if (isCountryHidden) {
              console.log('Skipped competition:', competitionName, '(country span is hidden)');
              return;
            }

            // Get competition logo
            const competitionLogo = competitionDiv.querySelector('img');
            const competitionLogoUrl = competitionLogo ? (competitionLogo.getAttribute('src') || competitionLogo.getAttribute('origin-src')) : null;

            // Get the second anchor tag (game info)
            const gameAnchor = itemWrap.querySelector('a.kAx4Cs');
            if (!gameAnchor) {
              console.log('No game anchor found in item-wrap', index + 1);
              return;
            }

            let gameUrl = gameAnchor.getAttribute('href');
            // Ensure gameUrl is an absolute URL by resolving it against the current page's URL
            if (gameUrl && !gameUrl.startsWith('http')) {
              gameUrl = new URL(gameUrl, window.location.href).href;
            }
            console.log('Game URL:', gameUrl);

            // Get game time
            const timeDiv = gameAnchor.querySelector('.RIPS\\\\+p');
            let gameTime = 'N/A';
            let currentMinute = '';

            if (timeDiv) {
              const timeSpan = timeDiv.querySelector('span:not(.s6v7bH):not(.uSpUtY)');
              if (timeSpan) {
                gameTime = timeSpan.textContent.trim();
              }

              // Get current minute if game is live
              const minuteSpan = timeDiv.querySelector('.kbCW\\\\+R span:not([style*="display: none"])');
              if (minuteSpan && minuteSpan.textContent.trim()) {
                currentMinute = minuteSpan.textContent.trim();
              }
            }

            // Check if this is a single event (like Apex Legends) or a match
            const matchDiv = gameAnchor.querySelector('.Rrze9z');
            let gameData = {};

            // Determine if it's a "single event" by checking for the presence of a specific title class
            // AND the absence of typical team name spans.
            const isSingleEvent = matchDiv &&
                                  matchDiv.classList.contains('rEvTCt') &&
                                  matchDiv.querySelector('._3PYXPF') && // Specific title class for events
                                  !matchDiv.querySelector('.T5er3y ._2u2Xtc'); // No team name span in home team div

            if (isSingleEvent) {
              // Single event (like Apex Legends)
              const eventTitle = matchDiv.querySelector('._3PYXPF');
              gameData = {
                type: 'event',
                title: eventTitle ? eventTitle.textContent.trim() : 'N/A',
                time: gameTime,
                current_minute: currentMinute,
                url: gameUrl,
                home_team: { name: 'N/A', score: 'N/A', logo: null },
                away_team: { name: 'N/A', score: 'N/A', logo: null }
              };
            } else if (matchDiv) {
              // Regular match with teams (either started or unstarted)
              const homeTeamDiv = matchDiv.querySelector('.T5er3y');
              const awayTeamDiv = matchDiv.querySelector('.Ld7BaR');
              const scoresContainer = matchDiv.querySelector('._8NFEJw');

              let homeTeamName = 'N/A', homeTeamLogo = null, homeScore = 'N/A';
              let awayTeamName = 'N/A', awayTeamLogo = null, awayScore = 'N/A';

              // Extract home team info
              if (homeTeamDiv) {
                const homeNameSpan = homeTeamDiv.querySelector('._2u2Xtc');
                const homeLogoImg = homeTeamDiv.querySelector('.r-logo');
                homeTeamName = homeNameSpan ? homeNameSpan.textContent.trim() : 'N/A';
                homeTeamLogo = homeLogoImg ? (homeLogoImg.getAttribute('src') || homeLogoImg.getAttribute('origin-src')) : null;
              }

              // Extract away team info
              if (awayTeamDiv) {
                const awayNameSpan = awayTeamDiv.querySelector('._2u2Xtc');
                const awayLogoImg = awayTeamDiv.querySelector('.r-logo');
                awayTeamName = awayNameSpan ? awayNameSpan.textContent.trim() : 'N/A';
                awayTeamLogo = awayLogoImg ? (awayLogoImg.getAttribute('src') || awayLogoImg.getAttribute('origin-src')) : null;
              }

              // Check for scores - they might be hidden for games that haven't started
              if (scoresContainer) {
                const scoresDiv = scoresContainer.querySelector('.Eu8BT4');
                const vsDiv = scoresContainer.querySelector('.B106Pj');

                // If there's a visible scores div, extract scores
                if (scoresDiv && scoresDiv.style.display !== 'none') {
                  const scoreSpans = scoresDiv.querySelectorAll('.FGXWhu .bw19CK');
                  if (scoreSpans.length >= 2) {
                    const homeScoreText = scoreSpans[0].textContent.trim();
                    const awayScoreText = scoreSpans[1].textContent.trim();
                    // Only use scores if they're not empty
                    if (homeScoreText && awayScoreText) {
                      homeScore = homeScoreText;
                      awayScore = awayScoreText;
                    }
                  }
                } else if (vsDiv && vsDiv.textContent.trim() === 'VS') {
                  // Game hasn't started yet - scores remain 'N/A'
                  console.log('Game hasn\\'t started yet:', homeTeamName, 'vs', awayTeamName);
                }
              }

              // If team names are still N/A, try to extract from URL
              if ((homeTeamName === 'N/A' || awayTeamName === 'N/A') && gameUrl && gameUrl.includes('-vs-')) {
                const urlParts = gameUrl.split('/').pop().replace('.html', '');
                const teamParts = urlParts.split('-vs-');
                if (teamParts.length === 2) {
                  let homeTeamPart = teamParts[0];
                  let awayTeamPart = teamParts[1];

                  // Remove numeric IDs
                  homeTeamPart = homeTeamPart.replace(/-[0-9]+\$/, '');
                  awayTeamPart = awayTeamPart.replace(/-[0-9]+\$/, '');

                  if (homeTeamName === 'N/A') {
                    homeTeamName = homeTeamPart.split('-').map(word => {
                      if (word.toLowerCase() === 'fc') return 'FC';
                      if (word.toLowerCase() === 'w') return '(W)';
                      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                    }).join(' ');
                  }

                  if (awayTeamName === 'N/A') {
                    awayTeamName = awayTeamPart.split('-').map(word => {
                      if (word.toLowerCase() === 'fc') return 'FC';
                      if (word.toLowerCase() === 'w') return '(W)';
                      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                    }).join(' ');
                  }

                  console.log('Extracted team names from URL for match div:', homeTeamName, 'vs', awayTeamName);
                }
              }

              gameData = {
                type: 'match',
                time: gameTime,
                current_minute: currentMinute,
                url: gameUrl,
                home_team: {
                  name: homeTeamName,
                  score: homeScore,
                  logo: homeTeamLogo
                },
                away_team: {
                  name: awayTeamName,
                  score: awayScore,
                  logo: awayTeamLogo
                }
              };
            } else {
              // No match div found, but we still have a game URL - try to extract team names from URL or create a basic match structure
              console.log('No match div found for game:', gameUrl);

              // Try to extract team names from the URL (usually in format: team1-vs-team2.html)
              let homeTeamName = 'N/A', awayTeamName = 'N/A';
              if (gameUrl && gameUrl.includes('-vs-')) {
                const urlParts = gameUrl.split('/').pop().replace('.html', '');
                const teamParts = urlParts.split('-vs-');
                if (teamParts.length === 2) {
                  // Clean up team names by removing competition/league identifiers and formatting properly
                  let homeTeamPart = teamParts[0];
                  let awayTeamPart = teamParts[1];

                  // Remove common prefixes that are competition identifiers
                  const competitionPrefixes = ['norwegian-eliteserien', 'finnish-veikkausliiga', 'belarusian-cup', 'international-club-friendly'];
                  competitionPrefixes.forEach(prefix => {
                    if (homeTeamPart.startsWith(prefix + '-')) {
                      homeTeamPart = homeTeamPart.substring(prefix.length + 1);
                    }
                  });

                  // Remove numeric IDs (like -4112103)
                  homeTeamPart = homeTeamPart.replace(/-[0-9]+\$/, '');
                  awayTeamPart = awayTeamPart.replace(/-[0-9]+\$/, '');

                  // Convert to proper case
                  homeTeamName = homeTeamPart.split('-').map(word => {
                    // Handle special cases
                    if (word.toLowerCase() === 'fc') return 'FC';
                    if (word.toLowerCase() === 'vs') return 'vs';
                    if (word.toLowerCase() === 'w') return '(W)';
                    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                  }).join(' ');

                  awayTeamName = awayTeamPart.split('-').map(word => {
                    // Handle special cases
                    if (word.toLowerCase() === 'fc') return 'FC';
                    if (word.toLowerCase() === 'vs') return 'vs';
                    if (word.toLowerCase() === 'w') return '(W)';
                    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                  }).join(' ');

                  console.log('Extracted team names from URL:', homeTeamName, 'vs', awayTeamName);
                }
              }

              gameData = {
                type: 'match',
                time: gameTime,
                current_minute: currentMinute,
                url: gameUrl,
                home_team: {
                  name: homeTeamName,
                  score: 'N/A',
                  logo: null
                },
                away_team: {
                  name: awayTeamName,
                  score: 'N/A',
                  logo: null
                }
              };
            }

            // Group games by competition
            if (!competitionsMap.has(competitionName)) {
              competitionsMap.set(competitionName, {
                competition: competitionName,
                country: countryText.replace(':&nbsp;', '').trim(),
                logo: competitionLogoUrl,
                games: []
              });
            }

            competitionsMap.get(competitionName).games.push(gameData);
          });
        }

        // Convert map to array
        competitionsMap.forEach((value) => {
          result.push(value);
        });

        console.log('Scraped data:', result);

        // Send the scraped data back to Flutter
        window.flutter_inappwebview.callHandler('scrapeHandler', result);
        return result;
      })();
    ''';
    return script;
  }

  static String getGoogleSitesScrapingScript() {
    const script = '''
      (function() {
        console.log('Google Sites scraping script started.');
        const extractedLinks = [];

        // Option 1: Look for divs with data-tooltip="🔗Link - something"
        // Then get the href of the a element inside.
        const linkDivs = document.querySelectorAll('div[data-tooltip^="🔗Link -"]');
        console.log('Found linkDivs:', linkDivs.length);
        for (const div of linkDivs) {
          const anchor = div.querySelector('a');
          if (anchor && anchor.hasAttribute('href')) {
            let href = anchor.getAttribute('href');
            if (href) {
              // Decode the Google URL redirect if present
              if (href.startsWith('https://www.google.com/url?q=')) {
                const urlParams = new URLSearchParams(href.split('?')[1]);
                href = urlParams.get('q');
              }
              if (href && !extractedLinks.includes(href)) {
                extractedLinks.push(href);
              }
            }
          }
        }

        // Option 2 (simpler): Look directly for anchors 'a' that have in their href 'https://www.google.com/url?q='
        const directAnchors = document.querySelectorAll('a[href^="https://www.google.com/url?q="]');
        console.log('Found directAnchors:', directAnchors.length);
        for (const anchor of directAnchors) {
          let href = anchor.getAttribute('href');
          if (href) {
            const urlParams = new URLSearchParams(href.split('?')[1]);
            href = urlParams.get('q');
            if (href && !extractedLinks.includes(href)) {
              extractedLinks.push(href);
            }
          }
        }

        console.log('Final extracted Google Sites links:', extractedLinks);
        window.flutter_inappwebview.callHandler('googleSitesScrapeHandler', extractedLinks);
        return extractedLinks;
      })();
    ''';
    return script;
  }

  static String getSiItemScrapingScript() {
    return '''
      (function() {
        console.log('SI Item scraping script started.');
        const result = [];
        let attempts = 0;
        const maxAttempts = 5;
        const delay = 1000; // 1 second

        function findAndScrape() {
          const serversDiv = document.querySelector('div[data-v-8ab3291c][class="servers"]');

          if (!serversDiv) {
            attempts++;
            if (attempts < maxAttempts) {
              console.log('Servers div not found, retrying in ' + delay + 'ms (Attempt ' + attempts + '/' + maxAttempts + ')');
              setTimeout(findAndScrape, delay);
            } else {
              console.log('Servers div not found after ' + maxAttempts + ' attempts. Stopping.');
              // Send empty result if not found
              if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                window.flutter_inappwebview.callHandler('siItemScrapeHandler', []);
              }
            }
            return;
          }

          console.log('Servers div found. Proceeding with scraping.');
          const siItems = serversDiv.querySelectorAll('.si-item');
          console.log('Found', siItems.length, 'si-item elements.');

        siItems.forEach((item, index) => {
          const link = item.querySelector('a');
          const nameSpan = item.querySelector('.name') || item.querySelector('.apk-text');

          let href = null;
          if (link) {
            href = link.getAttribute('href');
          }

          let name = 'N/A';
          if (nameSpan) {
            name = nameSpan.textContent.trim();
          }

          const itemData = {
            index: index,
            name: name,
            href: href
          };
          result.push(itemData);
          console.log('Scraped SI Item:', itemData);
        });

        // Send the scraped data back to Flutter (if a handler is registered)
        if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
          window.flutter_inappwebview.callHandler('siItemScrapeHandler', result);
        } else {
          console.warn('flutter_inappwebview.callHandler is not available. Data will only be logged to console.');
        }
        // No return here, as the handler is called asynchronously
      }

      // Start the scraping process
      findAndScrape();
    ''';
  }

  static String getServersAndSiItemsScrapingScript() {
    return '''
      (function() {
        console.log('Servers and SI Items scraping script started.');
        const result = [];
        let attempts = 0;
        const maxAttempts = 5;
        const delay = 1000; // 1 second

        function findAndScrape() {
          const serverItems = document.querySelectorAll('div.server-item');

          if (serverItems.length === 0) {
            attempts++;
            if (attempts < maxAttempts) {
              console.log('No server-item divs found, retrying in ' + delay + 'ms (Attempt ' + attempts + '/' + maxAttempts + ')');
              setTimeout(findAndScrape, delay);
            } else {
              console.log('No server-item divs found after ' + maxAttempts + ' attempts. Stopping.');
              if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                window.flutter_inappwebview.callHandler('serversAndSiItemsScrapeHandler', []);
              }
            }
            return;
          }

          console.log('Found ' + serverItems.length + ' server-item elements. Proceeding with scraping.');

          serverItems.forEach((serverItem, serverIndex) => {
            console.log('Scraping server-item ' + (serverIndex + 1) + ':', serverItem.outerHTML);
            const serverItemData = {
              serverIndex: serverIndex,
              outerHTML: serverItem.outerHTML,
              siItems: []
            };

            const siItems = serverItem.querySelectorAll('.si-item');
            console.log('  Found ' + siItems.length + ' si-item elements within this server-item.');

            siItems.forEach((siItem, siIndex) => {
              console.log('    Scraping si-item ' + (siIndex + 1) + ':', siItem.outerHTML);
              serverItemData.siItems.push({
                siIndex: siIndex,
                outerHTML: siItem.outerHTML
              });
            });
            result.push(serverItemData);
          });

          if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
            window.flutter_inappwebview.callHandler('serversAndSiItemsScrapeHandler', result);
          } else {
            console.warn('flutter_inappwebview.callHandler is not available. Data will only be logged to console.');
          }
        }

        findAndScrape();
      })();
    ''';
  }

  static String getPageHtmlScrapingScript() {
    return '''
      (function() {
        console.log('Full page HTML scraping script started.');
        const fullHtml = document.documentElement.outerHTML;
        console.log('Full page HTML length:', fullHtml.length);
        // Send the HTML back to Flutter
        if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
          window.flutter_inappwebview.callHandler('fullHtmlScrapeHandler', fullHtml);
        } else {
          console.warn('flutter_inappwebview.callHandler is not available. HTML will only be logged to console.');
        }
        return fullHtml;
      })();
    ''';
  }

  static Future<void> saveScrapedData(List<dynamic> data) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final cachePath = p.join(directory.path, 'cache');
      final file = File(p.join(cachePath, 'fixtures.json'));

      if (!await file.exists()) {
        await file.create(recursive: true);
        if (kDebugMode) {
          print('Created file: ${file.path}');
        }
      }

      final jsonString = jsonEncode(data);
      await file.writeAsString(jsonString);
      if (kDebugMode) {
        print('Scraped data saved to ${file.path}');
        print('Data size: ${jsonString.length} bytes');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving scraped data: $e');
      }
    }
  }

  static Future<void> saveHtmlToCache(
    String htmlContent,
    String filename,
  ) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final cachePath = p.join(directory.path, 'cache');
      final file = File(p.join(cachePath, filename));

      if (!await file.exists()) {
        await file.create(recursive: true);
        if (kDebugMode) {
          print('Created file: ${file.path}');
        }
      }

      await file.writeAsString(htmlContent);
      if (kDebugMode) {
        print('HTML content saved to ${file.path}');
        print('HTML size: ${htmlContent.length} bytes');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving HTML content: $e');
      }
    }
  }

  static Future<void> extractContent(InAppWebViewController controller) async {
    try {
      // Evaluate JavaScript to get the full HTML content
      final String? htmlContent = await controller.evaluateJavascript(
        source: WebViewUtils.getPageHtmlScrapingScript(),
      );

      if (htmlContent != null && htmlContent.isNotEmpty) {
        await WebViewUtils.saveHtmlToCache(
          htmlContent,
          'extracted_content.html',
        );
        if (kDebugMode) {
          print('Content extracted and saved to extracted_content.html');
        }
      } else {
        if (kDebugMode) {
          print('No content extracted or content is empty.');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting content: $e');
      }
    }
  }

  static bool shouldBlockUrl(String url, String channelUrl) {
    final currentDomain = getBaseDomain(channelUrl);
    final targetDomain = getBaseDomain(url);

    if (currentDomain.isEmpty || targetDomain.isEmpty) {
      return false; // Don't block if domains can't be parsed
    }

    if (currentDomain == targetDomain) {
      return false; // Don't block same-domain navigation
    }

    // Block cross-domain navigation to known ad servers or suspicious patterns
    final adPatterns = [
      'ads',
      'adserver',
      'advertising',
      'adservice',
      'doubleclick',
      'googleads',
      'googlesyndication',
      'pagead',
      'adnxs',
      'adzerk',
      'openx',
      'rubicon',
      'pubmatic',
      'moat',
      'scorecardresearch',
      'quantserve',
      'adsafeprotected',
      'adtech',
      'adform',
      'adition',
      'adblade',
      'adconion',
      'adinterax',
      'admeta',
      'admeld',
      'adview',
      'adwhirl',
      'adworx',
      'amazon-adsystem',
      'appnexus',
      'bidswitch',
      'bidvertiser',
      'bluekai',
      'brightroll',
      'casalemedia',
      'chartbeat',
      'chitika',
      'clicksor',
      'clicktale',
      'conversant',
      'crwdcntrl',
      'demdex',
      'dotomi',
      'doubleverify',
      'e-planning',
      'exelator',
      'exponential',
      'eyeota',
      'eyeview',
      'flashtalking',
      'gumgum',
      'indexexchange',
      'innovid',
      'integralads',
      'liveintent',
      'mediamath',
      'media.net',
      'mediamind',
      'millennialmedia',
      'mixpo',
      'mookie1',
      'outbrain',
      'platform-one',
      'pulsepoint',
      'quantcast',
      'revcontent',
      'rubiconproject',
      'serving-sys',
      'sharethrough',
      'sonobi',
      'spotx',
      'taboola',
      'teads',
      'tremorhub',
      'triplelift',
      'tubemogul',
      'undertone',
      'unruly',
      'weborama',
      'yieldlab',
      'yieldmo',
      'zedo',
      'adrecover',
      'adblock',
      'adguard',
      'adremover',
      'adstopper',
      'adterminator',
      'adzapper',
      'noads',
      'stopad',
      'ublock',
      'popup',
      'popunder',
      'poptastic',
      'onclickads',
      'onclick',
      'popads',
      'clickadu',
      'propellerads',
      'exoclick',
      'juicyads',
      'ero-advertising',
      'trafficjunky',
      'plugrush',
    ];

    for (final pattern in adPatterns) {
      if (url.contains(pattern)) {
        return true;
      }
    }

    return false;
  }
}
