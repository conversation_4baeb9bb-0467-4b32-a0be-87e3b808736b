import 'package:flutter/material.dart';
import 'dart:async';
import '../controllers/filter_controller.dart' as filter_ctrl;
import 'package:cat_tv/models/region.dart';
import 'package:cat_tv/models/country.dart';
import 'package:cat_tv/models/language.dart';
import 'package:cat_tv/models/category.dart' as cat_model;

class FilterWidget extends StatefulWidget {
  final filter_ctrl.FilterController controller;
  final List<Region> regions;
  final List<Country> countries;
  final List<Language> languages;
  final List<cat_model.Category> categories;
  final bool showFilters;

  const FilterWidget({
    super.key,
    required this.controller,
    required this.regions,
    required this.countries,
    required this.languages,
    required this.categories,
    required this.showFilters,
  });

  @override
  State<FilterWidget> createState() => _FilterWidgetState();
}

class MinimalistFilterWidget extends StatefulWidget {
  final filter_ctrl.FilterController controller;
  final List<Region> regions;
  final List<Country> countries;
  final List<Language> languages;
  final List<cat_model.Category> categories;

  const MinimalistFilterWidget({
    super.key,
    required this.controller,
    required this.regions,
    required this.countries,
    required this.languages,
    required this.categories,
  });

  @override
  State<MinimalistFilterWidget> createState() => _MinimalistFilterWidgetState();
}

class _MinimalistFilterWidgetState extends State<MinimalistFilterWidget>
    with TickerProviderStateMixin {
  bool _isExpanded = false;
  late TabController _tabController;
  late TextEditingController _searchController;
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _searchController = TextEditingController(
      text: widget.controller.filter.name,
    );
    widget.controller.addListener(_onFilterChanged);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _debounce?.cancel();
    widget.controller.removeListener(_onFilterChanged);
    super.dispose();
  }

  void _onFilterChanged() {
    if (_searchController.text != widget.controller.filter.name) {
      _searchController.text = widget.controller.filter.name ?? '';
    }
  }

  void _onSearchChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      final newValue = value.isEmpty ? null : value;
      widget.controller.setNameFilter(newValue);
    });
  }

  List<Widget> _buildSelectedFilters(BuildContext context) {
    final List<Widget> chips = [];
    final theme = Theme.of(context);
    final filter = widget.controller.filter;

    if (filter.categoryId != null) {
      final category = widget.categories.firstWhere(
        (cat) => cat.id == filter.categoryId,
        orElse: () => cat_model.Category(id: -1, name: 'Unknown'),
      );
      if (category.id != -1) {
        chips.add(
          _buildFilterChip(
            context,
            'Category: ${category.name}',
            () => widget.controller.clearFilter(category: true),
            theme,
          ),
        );
      }
    }

    if (filter.region != null) {
      final region = widget.regions.firstWhere(
        (r) => r.code == filter.region,
        orElse: () => Region(code: '', name: 'Unknown'),
      );
      if (region.code.isNotEmpty) {
        chips.add(
          _buildFilterChip(
            context,
            'Region: ${region.name}',
            () => widget.controller.clearFilter(region: true),
            theme,
          ),
        );
      }
    }

    if (filter.country != null) {
      final country = widget.countries.firstWhere(
        (c) => c.code == filter.country,
        orElse: () => Country(code: '', name: 'Unknown', flag: ''),
      );
      if (country.code.isNotEmpty) {
        chips.add(
          _buildFilterChip(
            context,
            'Country: ${country.flag} ${country.name}',
            () => widget.controller.clearFilter(country: true),
            theme,
          ),
        );
      }
    }

    if (filter.language != null) {
      final language = widget.languages.firstWhere(
        (l) => l.code == filter.language,
        orElse: () => Language(code: '', name: 'Unknown'),
      );
      if (language.code.isNotEmpty) {
        chips.add(
          _buildFilterChip(
            context,
            'Language: ${language.name}',
            () => widget.controller.clearFilter(language: true),
            theme,
          ),
        );
      }
    }

    return chips;
  }

  Widget _buildFilterChip(
    BuildContext context,
    String label,
    VoidCallback onDeleted,
    ThemeData theme,
  ) {
    return Chip(
      label: Text(
        label,
        style: TextStyle(color: theme.colorScheme.onSurface, fontSize: 12),
      ),
      deleteIcon: Icon(
        Icons.close,
        size: 16,
        color: theme.colorScheme.onSurface,
      ),
      onDeleted: onDeleted,
      backgroundColor: theme.colorScheme.surface.withValues(alpha: 0.8),
      side: BorderSide(color: theme.colorScheme.outline.withValues(alpha: 0.3)),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = MediaQuery.of(context).size.width < 600;

    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, _) {
        final selectedFilters = _buildSelectedFilters(context);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Search field and filter button row
            Row(
              children: [
                Expanded(child: _buildSearchField(context, theme, isMobile)),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                    if (_isExpanded && isMobile) {
                      _showMobileFilterBottomSheet(context);
                    }
                  },
                  icon: Icon(
                    Icons.filter_list_outlined,
                    color:
                        _isExpanded || selectedFilters.isNotEmpty
                            ? Colors.white
                            : Colors.grey,
                  ),
                  style: IconButton.styleFrom(
                    backgroundColor:
                        _isExpanded || selectedFilters.isNotEmpty
                            ? theme.colorScheme.primary.withValues(alpha: 0.1)
                            : Colors.transparent,
                    side: BorderSide(
                      color: theme.colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                ),
              ],
            ),

            // Selected filters chips
            if (selectedFilters.isNotEmpty) ...[
              const SizedBox(height: 8),
              Wrap(spacing: 8, runSpacing: 4, children: selectedFilters),
            ],

            // Desktop filter panel
            if (_isExpanded && !isMobile) ...[
              const SizedBox(height: 12),
              _buildDesktopFilterPanel(context, theme),
            ],
          ],
        );
      },
    );
  }

  Widget _buildSearchField(
    BuildContext context,
    ThemeData theme,
    bool isMobile,
  ) {
    return TextField(
      controller: _searchController,
      style: TextStyle(
        color: Colors.white, // Changed to white
        fontSize: isMobile ? 14 : 16,
      ),
      decoration: InputDecoration(
        labelText: 'Search channels',
        labelStyle: TextStyle(
          color: Colors.grey, // Changed to grey
          fontSize: isMobile ? 14 : 16,
        ),
        hintText: 'Enter channel name',
        hintStyle: TextStyle(
          color: Colors.grey,
          fontSize: isMobile ? 14 : 16,
        ), // Changed to grey
        prefixIcon: const Icon(Icons.search, color: Colors.white),
        floatingLabelBehavior: FloatingLabelBehavior.never,
        suffixIcon:
            widget.controller.filter.name != null &&
                    widget.controller.filter.name!.isNotEmpty
                ? IconButton(
                  icon: const Icon(
                    // Removed Icon() constructor
                    Icons.clear,
                    color: Colors.white, // Changed to white
                  ),
                  onPressed: () {
                    widget.controller.setNameFilter(null);
                  },
                )
                : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.primary),
        ),
        filled: true,
        fillColor: theme.colorScheme.surface.withValues(alpha: 0.1),
        contentPadding: EdgeInsets.symmetric(
          vertical: isMobile ? 12 : 16,
          horizontal: 16,
        ),
      ),
      onChanged: _onSearchChanged,
    );
  }

  Widget _buildDesktopFilterPanel(BuildContext context, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
          width: 1.0,
        ),
      ),
      child: Column(
        children: [
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Category'),
              Tab(text: 'Region'),
              Tab(text: 'Country'),
              Tab(text: 'Language'),
            ],
            labelColor: Colors.white,
            unselectedLabelColor: Colors.grey,
            indicatorColor: theme.colorScheme.primary,
            dividerColor: Colors.transparent,
          ),
          SizedBox(
            height: 200,
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCategoryTab(context, theme),
                _buildRegionTab(context, theme),
                _buildCountryTab(context, theme),
                _buildLanguageTab(context, theme),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showMobileFilterBottomSheet(BuildContext context) {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.7,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.9),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Expanded(
                  child: DefaultTabController(
                    length: 4,
                    child: Column(
                      children: [
                        TabBar(
                          tabs: const [
                            Tab(text: 'Category'),
                            Tab(text: 'Region'),
                            Tab(text: 'Country'),
                            Tab(text: 'Language'),
                          ],
                          labelColor: Colors.white,
                          unselectedLabelColor: Colors.grey,
                          indicatorColor: theme.colorScheme.primary,
                          dividerColor: Colors.transparent,
                        ),
                        Expanded(
                          child: TabBarView(
                            children: [
                              _buildCategoryTab(context, theme),
                              _buildRegionTab(context, theme),
                              _buildCountryTab(context, theme),
                              _buildLanguageTab(context, theme),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
    ).then((_) {
      setState(() {
        _isExpanded = false;
      });
    });
  }

  Widget _buildCategoryTab(BuildContext context, ThemeData theme) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.categories.length + 1,
      itemBuilder: (context, index) {
        if (index == 0) {
          return _buildFilterOption(
            context,
            theme,
            'All Categories',
            widget.controller.filter.categoryId == null,
            () => widget.controller.clearFilter(category: true),
          );
        }
        final category = widget.categories[index - 1];
        return _buildFilterOption(
          context,
          theme,
          category.name,
          widget.controller.filter.categoryId == category.id,
          () => widget.controller.setFilter(categoryId: category.id),
        );
      },
    );
  }

  Widget _buildRegionTab(BuildContext context, ThemeData theme) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.regions.length + 1,
      itemBuilder: (context, index) {
        if (index == 0) {
          return _buildFilterOption(
            context,
            theme,
            'All Regions',
            widget.controller.filter.region == null,
            () => widget.controller.clearFilter(region: true),
          );
        }
        final region = widget.regions[index - 1];
        return _buildFilterOption(
          context,
          theme,
          region.name,
          widget.controller.filter.region == region.code,
          () => widget.controller.setFilter(region: region.code, country: null),
        );
      },
    );
  }

  Widget _buildCountryTab(BuildContext context, ThemeData theme) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.countries.length + 1,
      itemBuilder: (context, index) {
        if (index == 0) {
          return _buildFilterOption(
            context,
            theme,
            'All Countries',
            widget.controller.filter.country == null,
            () => widget.controller.clearFilter(country: true),
          );
        }
        final country = widget.countries[index - 1];
        return _buildFilterOption(
          context,
          theme,
          '${country.flag} ${country.name}',
          widget.controller.filter.country == country.code,
          () => widget.controller.setFilter(country: country.code),
        );
      },
    );
  }

  Widget _buildLanguageTab(BuildContext context, ThemeData theme) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.languages.length + 1,
      itemBuilder: (context, index) {
        if (index == 0) {
          return _buildFilterOption(
            context,
            theme,
            'All Languages',
            widget.controller.filter.language == null,
            () => widget.controller.clearFilter(language: true),
          );
        }
        final language = widget.languages[index - 1];
        return _buildFilterOption(
          context,
          theme,
          language.name,
          widget.controller.filter.language == language.code,
          () => widget.controller.setFilter(language: language.code),
        );
      },
    );
  }

  Widget _buildFilterOption(
    BuildContext context,
    ThemeData theme,
    String title,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          color: Colors.white,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      trailing:
          isSelected ? const Icon(Icons.check, color: Colors.white) : null,
      onTap: onTap,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      tileColor:
          isSelected
              ? theme.colorScheme.primary.withValues(alpha: 0.1)
              : Colors.transparent,
    );
  }
}

class _FilterWidgetState extends State<FilterWidget> {
  late TextEditingController _searchController;
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(
      text: widget.controller.filter.name,
    );
    widget.controller.addListener(_onFilterChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    widget.controller.removeListener(_onFilterChanged);
    super.dispose();
  }

  void _onFilterChanged() {
    if (_searchController.text != widget.controller.filter.name) {
      _searchController.text = widget.controller.filter.name ?? '';
    }
  }

  void _onSearchChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      final newValue = value.isEmpty ? null : value;
      widget.controller.setNameFilter(newValue);
    });
  }

  Widget _buildDropdown<T>(
    BuildContext context,
    String hintText,
    T? value,
    List<DropdownMenuItem<T>> items,
    ValueChanged<T?> onChanged,
    String allOptionText,
    bool shouldExpand,
  ) {
    return DropdownButtonFormField<T>(
      isExpanded: shouldExpand,
      value: value,
      decoration: InputDecoration(
        labelText: hintText,
        labelStyle: const TextStyle(color: Colors.white70),
        filled: true,
        fillColor: Colors.white.withOpacity(0.1),
        contentPadding:
            shouldExpand
                ? const EdgeInsets.symmetric(horizontal: 12, vertical: 16)
                : const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.2)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.2)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
        ),
      ), // Corrected closing parenthesis for InputDecoration
      dropdownColor: Colors.black.withOpacity(
        0.8,
      ), // More opaque for better readability
      style: const TextStyle(color: Colors.white, fontSize: 14),
      iconEnabledColor: Colors.white,
      items: [
        DropdownMenuItem<T>(
          value: null,
          child: Text(
            allOptionText,
            style: const TextStyle(color: Colors.white),
          ),
        ),
        ...items,
      ],
      onChanged: onChanged,
    );
  }

  Widget _buildResetButton(BuildContext context, bool isMobile) {
    return ElevatedButton.icon(
      onPressed: () {
        widget.controller.resetAll();
      },
      icon: const Icon(Icons.refresh, size: 20),
      label: Text('Reset', style: TextStyle(fontSize: isMobile ? 14 : 16)),
      style: ElevatedButton.styleFrom(
        minimumSize:
            isMobile ? const Size.fromHeight(40) : const Size.fromHeight(48),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Colors.white.withOpacity(0.1),
        foregroundColor: Colors.white,
        elevation: 0,
        side: BorderSide(color: Colors.white.withOpacity(0.2)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, _) {
        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isMobile ? 12 : 16,
            vertical: isMobile ? 8 : 12,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Search field is always visible
              _SearchField(
                controller: _searchController,
                filterController: widget.controller,
                onSearchChanged: _onSearchChanged,
                isMobile: isMobile,
              ),
              // Show filters only when showFilters is true
              if (widget.showFilters) ...[
                const SizedBox(height: 12),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(isMobile ? 12 : 16),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                      width: 1.0,
                    ),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(isMobile ? 12 : 16),
                    child:
                        isMobile
                            ? _buildMobileLayout(context)
                            : _buildDesktopLayout(context),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildDropdown(
          context,
          'Category',
          widget.controller.filter.categoryId,
          widget.categories
              .map(
                (cat) => DropdownMenuItem(
                  value: cat.id,
                  child: Text(
                    cat.name,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              )
              .toList(),
          (val) {
            if (val == null) {
              widget.controller.clearFilter(category: true);
            } else {
              widget.controller.setFilter(categoryId: val);
            }
          },
          'All Categories',
          true,
        ),
        const SizedBox(height: 8),
        _buildDropdown(
          context,
          'Region',
          widget.controller.filter.region,
          widget.regions
              .map(
                (r) => DropdownMenuItem(
                  value: r.code,
                  child: Text(
                    r.name,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              )
              .toList(),
          (val) {
            if (val == null) {
              widget.controller.clearFilter(region: true);
            } else {
              widget.controller.setFilter(region: val, country: null);
            }
          },
          'All Regions',
          true,
        ),
        const SizedBox(height: 8),
        _buildDropdown(
          context,
          'Country',
          widget.controller.filter.country,
          widget.countries
              .map(
                (c) => DropdownMenuItem(
                  value: c.code,
                  child: Row(
                    children: [
                      if (c.flag.isNotEmpty) Text(c.flag),
                      if (c.flag.isNotEmpty) const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          c.name,
                          style: const TextStyle(color: Colors.white),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
          (val) {
            if (val == null) {
              widget.controller.clearFilter(country: true);
            } else {
              widget.controller.setFilter(country: val);
            }
          },
          'All Countries',
          true,
        ),
        const SizedBox(height: 8),
        _buildDropdown(
          context,
          'Language',
          widget.controller.filter.language,
          widget.languages
              .map(
                (l) => DropdownMenuItem(
                  value: l.code,
                  child: Text(
                    l.name,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              )
              .toList(),
          (val) {
            if (val == null) {
              widget.controller.clearFilter(language: true);
            } else {
              widget.controller.setFilter(language: val);
            }
          },
          'All Languages',
          true,
        ),
        const SizedBox(height: 12),
        _buildResetButton(context, true),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Column(
      children: [
        Wrap(
          spacing: 12.0,
          runSpacing: 12.0,
          alignment: WrapAlignment.start,
          children: [
            SizedBox(
              width: 200,
              child: _buildDropdown(
                context,
                'Category',
                widget.controller.filter.categoryId,
                widget.categories
                    .map(
                      (cat) => DropdownMenuItem(
                        value: cat.id,
                        child: Text(
                          cat.name,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    )
                    .toList(),
                (val) {
                  if (val == null) {
                    widget.controller.clearFilter(category: true);
                  } else {
                    widget.controller.setFilter(categoryId: val);
                  }
                },
                'All Categories',
                true,
              ),
            ),
            SizedBox(
              width: 200,
              child: _buildDropdown(
                context,
                'Region',
                widget.controller.filter.region,
                widget.regions
                    .map(
                      (r) => DropdownMenuItem(
                        value: r.code,
                        child: Text(
                          r.name,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    )
                    .toList(),
                (val) {
                  if (val == null) {
                    widget.controller.clearFilter(region: true);
                  } else {
                    widget.controller.setFilter(region: val, country: null);
                  }
                },
                'All Regions',
                true,
              ),
            ),
            SizedBox(
              width: 200,
              child: _buildDropdown(
                context,
                'Country',
                widget.controller.filter.country,
                widget.countries
                    .map(
                      (c) => DropdownMenuItem(
                        value: c.code,
                        child: Row(
                          children: [
                            if (c.flag.isNotEmpty) Text(c.flag),
                            if (c.flag.isNotEmpty) const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                c.name,
                                style: const TextStyle(color: Colors.white),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                    .toList(),
                (val) {
                  if (val == null) {
                    widget.controller.clearFilter(country: true);
                  } else {
                    widget.controller.setFilter(country: val);
                  }
                },
                'All Countries',
                true,
              ),
            ),
            SizedBox(
              width: 200,
              child: _buildDropdown(
                context,
                'Language',
                widget.controller.filter.language,
                widget.languages
                    .map(
                      (l) => DropdownMenuItem(
                        value: l.code,
                        child: Text(
                          l.name,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    )
                    .toList(),
                (val) {
                  if (val == null) {
                    widget.controller.clearFilter(language: true);
                  } else {
                    widget.controller.setFilter(language: val);
                  }
                },
                'All Languages',
                true,
              ),
            ),
            _buildResetButton(context, false),
          ],
        ),
      ],
    );
  }
}

class _SearchField extends StatelessWidget {
  final TextEditingController controller;
  final filter_ctrl.FilterController filterController;
  final ValueChanged<String> onSearchChanged;
  final bool isMobile;

  const _SearchField({
    required this.controller,
    required this.filterController,
    required this.onSearchChanged,
    required this.isMobile,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      style: TextStyle(color: Colors.white, fontSize: isMobile ? 14 : 16),
      decoration: InputDecoration(
        labelText: 'Search by name',
        labelStyle: TextStyle(
          color: Colors.grey, // Changed to grey
          fontSize: isMobile ? 14 : 16,
        ),
        hintText: 'Enter channel name',
        hintStyle: TextStyle(
          color: Colors.grey, // Changed to grey
          fontSize: isMobile ? 14 : 16,
        ),
        prefixIcon: const Icon(Icons.search, color: Colors.white),
        suffixIcon:
            filterController.filter.name != null &&
                    filterController.filter.name!.isNotEmpty
                ? IconButton(
                  icon: const Icon(Icons.clear, color: Colors.white),
                  onPressed: () {
                    filterController.setNameFilter(null);
                  },
                )
                : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.2)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.2)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
        ),
        filled: true,
        fillColor: Colors.white.withOpacity(0.1),
        contentPadding:
            isMobile
                ? const EdgeInsets.symmetric(vertical: 12, horizontal: 16)
                : const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      ),
      onChanged: onSearchChanged,
    );
  }
}
